@extends('layout.main')
@section('title', 'اتصل بنا')
@section('content')
<!-- Contact Start -->
<div class="container-fluid wow fadeInUp" data-wow-delay="0.1s">
    <div class="container">
        <div class="section-title text-center position-relative pb-3 mb-5 mx-auto" style="max-width: 100%;">
            <h5 class="fw-bold text-primary font-alhuraa">الاتصال بنا</h5>
            <h2 class="mb-0 font-alhuraa">إذا كان لديك أي استفسار ، فلا تتردد في الاتصال بنا</h2>
        </div>
        <div class="row g-5 mb-5 ">
            <div class="col-lg-4">
                <div class="d-flex align-items-center wow fadeIn" data-wow-delay="0.1s">
                    <div class="bg-primary d-flex align-items-center justify-content-center rounded" style="width: 60px; height: 60px;">
                        <i class="fa fa-phone-alt text-white"></i>
                    </div>
                    <div class="ps-4">
                        <h6 class="mb-2 font-kufi-regular">اتصل لطرح أي سؤال</h6>
                        <h5 class="text-primary mb-0">009647700000000</h5>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="d-flex align-items-center wow fadeIn" data-wow-delay="0.4s">
                    <div class="bg-primary d-flex align-items-center justify-content-center rounded" style="width: 60px; height: 60px;">
                        <i class="fa fa-envelope-open text-white"></i>
                    </div>
                    <div class="ps-4">
                        <h6 class="mb-2 font-kufi-regular">البريد الالكتروني</h6>
                        <h5 class="text-primary mb-0"><EMAIL></h5>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="d-flex align-items-center wow fadeIn" data-wow-delay="0.8s">
                    <div class="bg-primary d-flex align-items-center justify-content-center rounded" style="width: 60px; height: 60px;">
                        <i class="fa fa-map-marker-alt text-white"></i>
                    </div>
                    <div class="ps-4">
                        <h6 class="mb-2 font-kufi-regular">العنوان</h6>
                        <h5 class="text-primary mb-0 font-kufi-regular">كركوك، طريق بغداد</h5>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-5 font-kufi-regular">
            <div class="col-lg-6 wow slideInUp" data-wow-delay="0.3s">
                @if(Session::has('success'))
                <div class="alert alert-success text-center">
                    {{ Session::get('success') }}
                </div>
                @endif
                <form action="{{ route('contactus.sendmsg') }}" method="POST">
                    @csrf
                    <div class="row g-3">
                        <div class="col-md-6">
                            <input type="text" class="form-control border-0 bg-light px-4 @error('name') is-invalid @enderror" placeholder="اسمك الكامل" value="{{ old('name') }}" name="name" id="inputName" style="height: 55px;">
                            @error('name')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-6">
                            <input type="email" class="form-control border-0 bg-light px-4 @error('email') is-invalid @enderror" placeholder="البريد الالكتروني" value="{{ old('email') }}" name="email" id="inputEmail" style="height: 55px;">
                            @error('email')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control border-0 bg-light px-4 @error('phone') is-invalid @enderror" placeholder="رقم الهاتف" value="{{ old('phone') }}" name="phone" id="inputphone" style="height: 55px;">
                            @error('phone')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-12">
                            <textarea class="form-control border-0 bg-light px-4 py-3 @error('message') is-invalid @enderror" name="message" rows="4" placeholder="الرسالة">{{ old('message') }}</textarea>
                            @error('message')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div style="width: 300px; margin: 20px auto!important">
                            <div class="form-group mb-4">
                                {!! NoCaptcha::renderJs() !!}
                                {!! NoCaptcha::display() !!}
                                <span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
                            </div>
                        </div>
                        <div class="col-12">
                            <button class="btn btn-primary w-100 py-3" type="submit">ارسال</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-lg-6 wow slideInUp" data-wow-delay="0.6s">
                <iframe class="position-relative rounded w-100 h-100" src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d26019.63744898747!2d44.3513988!3d35.3939788!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1554d9055274f287%3A0x421435e556e6b93a!2z2K_ZitmI2KfZhiDZhdit2KfZgdi42Kkg2YPYsdmD2YjZgw!5e0!3m2!1sar!2siq!4v1667324249456!5m2!1sar!2siq" frameborder="0" style="min-height: 350px; border:0;" allowfullscreen="" aria-hidden="false" tabindex="0"></iframe>
            </div>
        </div>
    </div>
</div>
<!-- Contact End -->
@endsection