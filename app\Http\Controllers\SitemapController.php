<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\News;
class SitemapController extends Controller
{
	public function index() 
	{
		$newsSitemap = News::all();
		return response()->view('sitemap.index', ['newsSitemap' => $newsSitemap])->header('Content-Type', 'text/xml');
	}
	public function news()
	{
		$news = News::orderBy('NewsID', 'DESC')->get();
		return response()->view('sitemap.news', ['news' => $news])->header('Content-Type', 'text/xml');
	}
}
