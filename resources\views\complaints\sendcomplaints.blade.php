@extends('layout.main')



@section('title', 'تقديم شكوى جديدة')



@section('content')

<section class="w-50 m-auto">

	<div class="contact-form text-right font-kufi-regular">

		<form class="form-js2" action="{{ route('complaints.store') }}" method="POST" enctype="multipart/form-data">

			@csrf

			<div class="form-group mb-3">

				<label for="inputName">الموضوع </label>

				<input type="text" name="subject" class="@error('subject') is-invalid @enderror form-control" value="{{ old('subject') }}" id="inputName">

				@error('subject')

				<span class="text-danger">{{ $message }}</span>

				@enderror

			</div>

			<div class="form-group mb-3">

				<label for="inputphone">رقم الهاتف </label>

				<input type="number" name="phone" class="@error('phone') is-invalid @enderror form-control" value="{{ old('phone') }}" id="inputphone">

				@error('phone')

				<span class="text-danger">{{ $message }}</span>

				@enderror

			</div>

			<div class="form-group mb-3">

				<label for="inputcompany">اسم شركة الدفع الالكتروني او اسم بطاقة الماستر كارد الخاصة بالدفع </label>

				<input type="text" name="company" class="@error('company') is-invalid  @enderror form-control" value="{{ old('company') }}" id="inputcompany">

				@error('company')

				<span class="text-danger">{{ $message }}</span>

				@enderror

			</div>

			<div class="form-group mb-3">

				<label for="inputMessage">نص الشكوى</label>

				<textarea name="msg" class="@error('msg') is-invalid @enderror form-control" id="inputMessage" value="{{ old('msg') }}" rows="5"></textarea>

				@error('msg')

				<span class="text-danger">{{ $message }}</span>

				@enderror

			</div>

			<div class="form-group mb-3">

				<label for="inputName1">حمل ملف 1</label>

				<input type="file" name="file1" accept="image/gif, image/jpg, image/jpeg, image/png, application/pdf" class="@error('file1') is-invalid @enderror form-control" id="inputName1">

				@error('file1')

				<span class="text-danger">{{ $message }}</span>

				@enderror

			</div>

			<div class="form-group mb-3">

				<label for="inputName2">حمل ملف 2</label>

				<input type="file" accept="image/gif, image/jpg, image/jpeg, image/png, application/pdf,application/vnd.ms-excel,application/msword" name="file2" class="@error('file2') is-invalid @enderror form-control" id="inputName2">

				@error('file2')

				<span class="text-danger">{{ $message }}</span>

				@enderror

			</div>

			<hr>

			<button type="submit" class="btn btn-primary btn-block"><i class="fa fa-paper-plane"></i> تقديم الشكوى</button>

		</form>

	</div>

</section>

@endsection