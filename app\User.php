<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Complaint;
use App\Comment;
class User extends Authenticatable
{
    use Notifiable;
    protected $table = 'usercomplaints';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'username', 'password',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password'
    ];
    public function complaints()
    {
        return $this->hasMany(Complaint::class);
    }
    public function comment()
    {
        return $this->hasMany(Comment::class);
    }
}
