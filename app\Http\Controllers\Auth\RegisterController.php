<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use App\User;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Auth\Events\Registered;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/login';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */

    protected function validator(array $data)
    {
        return Validator::make($data, [
            'name' => ['required', 'string', 'max:100', 'min:5'],
            'username' => ['required', 'max:100', 'min:3', 'unique:usercomplaints'],
            'password' => ['required', 'string', 'min:6', 'confirmed'],
            'g-recaptcha-response' => 'required|captcha',
        ], [
            'name.required' => 'الاسم الكامل مطلوب.',
            'name.max' => 'الاسم الكامل يجب ان لايكون اكثر من 100 حرف.',
            'name.min' => 'الاسم الكامل يجب ان لايكون اقل من 5 حرف.',
            'username.required' => 'اسم المستخدم مطلوب.',
            'username.max' => 'اسم المستخدم لايمكن ان يكون اكثر من 100 حرف.',
            'username.min' => 'اسم المستخدم لايمكن ان يكون اقل من 3 حرف.',
            'username.unique' => 'اسم المستخدم مسجل مسبقا.',
            'password.required' => 'كلمة السر مطلوبة.',
            'password.min' => 'يجب ان تكون كلمة السر اكثر من 6 ارقام .',
            'password.confirmed' => 'كلمة السر غير متطابقة .',
            'g-recaptcha-response.required' => 'التحقق مطلوب.',
            'g-recaptcha-response.captcha' => 'خطأ في التحقق.',
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\User
     */
    protected function create(array $data)
    {

        $users = User::create([
            'name' => $data['name'],
            'username' => $data['username'],
            'password' => Hash::make($data['password']),
        ]);
        session()->flash('notification', 'تم التسجيل بنجاح');
        return $users;
    }
    /**
     * Handle a registration request for the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function register(Request $request)
    {
        $this->validator($request->all())->validate();

        event(new Registered($user = $this->create($request->all())));

        //The auto login code has been removed from here.

        return redirect($this->redirectPath());
    }
}
