<!DOCTYPE html>
<html dir="rtl">

<head>
    <meta charset="utf-8">
    <meta name="twitter:widgets:autoload" content="off">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta property="og:url" content="{{ url('') }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>استمارة اصدار هوية موظفي مجلس محافظة كركوك</title>
    <!-- Css Files -->
    <link rel="shortcut icon" href="{{ asset('public/img/favicon.ico') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/css/datepicker.min.css') }}">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"
        integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/css/all.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/css/cropper.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/lib/select2/css/select2.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/lib/select2/css/select2-bootstrap.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/css/form.css') }}">
    <style>
        .select2-results__options {
            text-align: right
        }
    </style>
</head>

<body>
    <div>
        <button type="button" class="btn btn-primary" onclick="forceReload();">تحديث الصفحة</button>
    </div>
    <div class="modal fade" id="CropModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="CropModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="CropModalLabel">اقتصاص الصورة</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="cropimg" style="direction: ltr;">
                        <div class="row">
                            <div class="col-md-8">
                                <div style="min-height: 497px;max-height: 497px">
                                    <img src="" id="sample_image" />
                                </div>

                            </div>
                            <div class="col-md-4 text-center">
                                <div class="preview"></div>
                                <div class="btn-group mt-5" style="display: inherit!important;">
                                    <button type="button" class="btn btn-primary" title="Zoom In" id="Zoom_In">
                                        <span>
                                            <span class="fa fa-search-plus"></span>
                                        </span>
                                    </button>
                                    <button type="button" class="btn btn-primary" title="Zoom Out" id="Zoom_Out">
                                        <span>
                                            <span class="fa fa-search-minus"></span>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="crop">اقتصاص</button>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="custom-form">
            <h4 class="text-center">استمارة اصدار هوية موظفي مجلس محافظة كركوك</h4>
            <hr width="100%" style="margin: auto; padding-bottom: 20px; padding-top: 10px">
            @if (session()->has('successsendidenf'))
            <div class="alert alert-success text-center">
                {!! session('successsendidenf') !!}
            </div>
            @else
            <form action="{{ route('form.kirkukprovincialcouncil.identity.store') }}" style="position: relative;"
                method="POST" enctype="multipart/form-data">
                @csrf
                <div class="form-group row text-right">
                    <label class="col-sm-3 col-form-label">الحالة</label>
                    <div class="col-sm-4">
                        <select name="f1" class="form-control changestatus @error('f1') is-invalid @enderror" required>
                            <option value="">اختر...</option>
                            <option value="هوية مجلس">هوية مجلس</option>
                            <option value="عضو مجلس">عضو مجلس</option>
                            <option value="تابع عضو">تابع عضو</option>
                        </select>
                        @error('f1')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>
                <div class="text-right" id="ifYes">
                    <div class="col-sm-3 d-flex flex-column align-items-center mb-2"
                        style="position: absolute; left: 0;top: -20px;">
                        <label for="upload_image" class="btn btn-outline-secondary mb-2">الصورة الشخصية</label>
                        <input type="file" class="d-none" id="upload_image" data-type="upload1">
                        <input type="hidden" id="upload1" name="f2">
                        <img style="width: 150px!important;" id="uploaded_image" src="">
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">الاسم الثلاثي</label>
                        <div class="col-sm-4">
                            <input type="text" name="f3" value="{{ old('f3') }}"
                                class="form-control @error('f3') is-invalid @enderror" required>
                            @error('f3')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row" id="hiddennameen">
                        <label class="col-sm-3 col-form-label">الاسم باللغة الانكليزية</label>
                        <div class="col-sm-4">
                            <input type="text" id="requireCustom1" name="f4" value="{{ old('f4') }}" pattern="\S(.*\S)?"
                                class="form-control @error('f4') is-invalid @enderror"
                                onkeyup="this.value = this.value.toUpperCase();" required>
                            @error('f4')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row" id="hiddenstatus">
                        <label class="col-sm-3 col-form-label label-656FGA766">العنوان الوظيفي</label>
                        <div class="col-sm-4 select-656FGA766">
                            <select class="display-results" data-searchable='searchable' name="ff7" required
                                style="width: 100%;">
                                <option value="">اختر...</option>
                                @foreach (\Illuminate\Support\Facades\DB::table('jobs_and_section_title')
                                ->where('type', 1)->get() as $item)
                                <option value="{{$item->title}}">{{$item->title}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="form-group row" id="hiddenstatus2">
                    </div>
                    <div class="form-group row hiddeninputs-652hg76GHF6">
                        <label class="col-sm-3 col-form-label">الرقم الوظيفي</label>
                        <div class="col-sm-4">
                            <input type="text" name="job_number" class="form-control" required>
                        </div>
                    </div>
                    <div class="form-group row hiddeninputs">
                        <label class="col-sm-3 col-form-label" id="hiddencat">القسم او الشعبة</label>
                        <div class="col-sm-4">
                            <select class="display-results" data-searchable='searchable' name="f6" required
                                style="width: 100%;">
                                <option value="">اختر...</option>
                                @foreach (\Illuminate\Support\Facades\DB::table('jobs_and_section_title')
                                ->where('type', 2)->get() as $item)
                                <option value="{{$item->title}}">{{$item->title}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">رقم الهاتف</label>
                        <div class="col-sm-4">
                            <input type="number" name="f10" value="{{ old('f10') }}"
                                class="form-control @error('f10') is-invalid @enderror" required>
                            @error('f10')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">الحالة الزوجية</label>
                        <div class="col-sm-4">
                            <select name="f1new" class="form-control @error('f1new') is-invalid @enderror" required>
                                <option value="">...</option>
                                <option value="متزوج">متزوج</option>
                                <option value="اعزب">اعزب</option>
                                <option value="ارمل">ارمل</option>
                                <option value="مطلق">مطلق</option>
                            </select>
                            @error('f1new')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">سنة التولد</label>
                        <div class="col-sm-4">
                            <select name="f2new" class="form-control @error('f2new') is-invalid @enderror" required>
                                <option value="">...</option>
                                @for ($i = date("Y") - 100; $i <= date("Y") - 18; $i++) <option value="{{$i}}">{{$i}}
                                    </option>
                                    @endfor
                            </select>
                            @error('f2new')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group mb-3 row">
                        <label class="col-sm-3 col-form-label">رقم البطاقه الوطنية</label>
                        <div class="col-sm-3">
                            <input type="text" class="form-control @error('f3new') is-invalid @enderror" name="f3new"
                                required>
                            @error('f3new')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <div class="col-sm-2">
                            <select class="form-control @error('f4new') is-invalid @enderror" name="f4new" required>
                                <option value="">محل اصدارها</option>
                                <option value="كركوك">كركوك</option>
                                <option value="بغداد">بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="ميسان">ميسان</option>
                                <option value="ذي قار">ذي قار</option>
                                <option value="الديوانية">الديوانية</option>
                                <option value="المثنى">المثنى</option>
                                <option value="النجف الاشرف">النجف الاشرف</option>
                                <option value="كربلاء المقدسة">كربلاء المقدسة</option>
                                <option value="بابل">بابل</option>
                                <option value="واسط">واسط</option>
                                <option value="ديالى">ديالى</option>
                                <option value="صلاح الدين">صلاح الدين</option>
                                <option value="نينوى">نينوى</option>
                                <option value="الانبار">الانبار</option>
                                <option value="اربيل">اربيل</option>
                                <option value="دهوك">دهوك</option>
                                <option value="سليمانية">سليمانية</option>
                            </select>
                            @error('f4new')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <label class="col-sm-2 col-form-label">تاريخ اصدارها</label>
                        <div class="col-sm-2">
                            <input type="date" class="form-control @error('f5new') is-invalid @enderror" name="f5new"
                                required>
                            @error('f5new')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">رقم الجواز</label>
                        <div class="col-sm-3">
                            <input type="text" class="form-control" name="f6new">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">عنوان السكن</label>
                        <div class="col-sm-3">
                            <input type="text" name="f13" value="{{ old('f13') }}"
                                class="form-control @error('f13') is-invalid @enderror" placeholder="المحافظة" required>
                            @error('f13')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>

                        <div class="col-sm-3">
                            <input type="text" name="f14" value="{{ old('f14') }}"
                                class="form-control @error('f14') is-invalid @enderror" placeholder="القضاء" required>
                            @error('f14')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <div class="col-sm-3">
                            <input type="text" name="f15" value="{{ old('f15') }}"
                                class="form-control @error('f15') is-invalid @enderror" placeholder="الناحيه" required>
                            @error('f15')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-3">
                            <input type="text" name="f16" value="{{ old('f16') }}" class="form-control"
                                placeholder="محلة" required>
                        </div>
                        <div class="col-sm-3">
                            <input type="text" name="f17" value="{{ old('f17') }}" class="form-control"
                                placeholder="زقاق" required>
                        </div>
                        <div class="col-sm-3">
                            <input type="text" name="f18" value="{{ old('f18') }}" class="form-control"
                                placeholder="دار" required>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">اقرب نقطة دالة</label>
                        <div class="col-sm-3">
                            <input type="text" name="f19" value="{{ old('f19') }}"
                                class="form-control @error('f19') is-invalid @enderror" required>
                            @error('f19')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">هل تملك مركبة خاصة او حكومية</label>
                        <div class="col-sm-6" id="hiddenNote">
                            <small class="text-danger">ملاحظة: هذا الحقل يملأ فقط في حالة كنت من احد الاقسام المشموله
                                بدخول مركبتكم في مراَب ديوان المحافظة او كنت تملك تصريح دخول</small>
                        </div>
                        <div class="col-sm-3">
                            <select class="form-control" id="requireCustom55" name="f20" onchange="yesnoCheck2(this);">
                                <option value="">اختر</option>
                                <option value="نعم">نعم</option>
                                <option value="لا">لا</option>
                            </select>
                        </div>
                        <div class="col-sm-6" id="customfile" style="display: none;">
                            <div class="custom-file">
                                <label class="custom-file-label text-center" for="inputGroupFile01">رفع نسخة من سنوية
                                    المركبة</label>
                                <input type="file" accept="image/*" name="f21" value="{{ old('f21') }}"
                                    class="custom-file-input @error('f21') is-invalid @enderror" id="inputGroupFile01"
                                    id="haveCar" aria-describedby="inputGroupFileAddon01">
                                @error('f21')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="form-group row" id="ifYes2" style="display: none;">
                        <div class="col-sm-4">
                            <input type="text" name="f22" id="haveCar1" value="{{ old('f22') }}" class="form-control"
                                placeholder="اسم صاحب السنوية">
                        </div>
                        <div class="col-sm-2">
                            <input type="text" name="f23" id="haveCar2" value="{{ old('f23') }}" class="form-control"
                                placeholder="رقمها">
                        </div>
                        <div class="col-sm-2">
                            <select class="form-control" name="f24" id="haveCar3">
                                <option value="">المحافظة</option>
                                <option value="حكومي">حكومي</option>
                                <option value="منفيس">منفيس</option>
                                <option value="كركوك">كركوك</option>
                                <option value="بغداد">بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="ميسان">ميسان</option>
                                <option value="ذي قار">ذي قار</option>
                                <option value="الديوانية">الديوانية</option>
                                <option value="المثنى">المثنى</option>
                                <option value="النجف الاشرف">النجف الاشرف</option>
                                <option value="كربلاء المقدسة">كربلاء المقدسة</option>
                                <option value="بابل">بابل</option>
                                <option value="واسط">واسط</option>
                                <option value="ديالى">ديالى</option>
                                <option value="صلاح الدين">صلاح الدين</option>
                                <option value="نينوى">نينوى</option>
                                <option value="الانبار">الانبار</option>
                                <option value="اربيل">اربيل</option>
                                <option value="دهوك">دهوك</option>
                                <option value="سليمانية">سليمانية</option>
                            </select>
                        </div>
                        <div class="col-sm-2">
                            <input type="text" name="f25" id="haveCar4" value="{{ old('f25') }}" class="form-control"
                                placeholder="نوعها">
                        </div>
                        <div class="col-sm-2">
                            <input type="text" name="f26" id="haveCar5" value="{{ old('f26') }}" class="form-control"
                                placeholder="موديلها">
                        </div>
                    </div>
                    <div class="form-group row" id="hiddenwepon">
                        <label class="col-sm-3 col-form-label">هل تملك سلاح</label>
                        <div class="col-sm-6" id="hiddenNote2">
                            <small class="text-danger">ملاحظة: هذا الحقل يملأ فقط في حالة كنت تملك تخويل حمل
                                السلاح.</small>
                        </div>
                        <div class="col-sm-3">
                            <select class="form-control" id="requireCustom66" name="f27" onchange="yesnoCheck3(this);">
                                <option value="">اختر</option>
                                <option value="نعم">نعم</option>
                                <option value="لا">لا</option>
                            </select>
                        </div>
                        <div class="col-sm-6" id="customfile2" style="display: none;">
                            <div class="custom-file">
                                <label class="custom-file-label text-center" for="inputGroupFile01">رفع نسخة من رخصة
                                    السلاح</label>
                                <input type="file" name="f28" accept="image/*" value="{{ old('f28') }}"
                                    class="custom-file-input @error('f28') is-invalid @enderror" id="inputGroupFile01"
                                    aria-describedby="inputGroupFileAddon01">
                                @error('f28')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="form-group row" id="ifYes3" style="display: none;">
                        <div class="col-sm-3">
                            <input type="text" name="f29" id="havewepone1" value="{{ old('f29') }}" class="form-control"
                                placeholder="نوع السلاح">
                        </div>
                        <div class="col-sm-3">
                            <input type="text" name="f30" id="havewepone2" value="{{ old('f30') }}" class="form-control"
                                placeholder="رقم السلاح ">
                        </div>
                        <div class="col-sm-3">
                            <input type="text" name="f31" id="havewepone3" value="{{ old('f31') }}" class="form-control"
                                placeholder="رقم الرخصة ">
                        </div>
                        <div class="col-sm-3">
                            <input type="text" data-toggle="datepicker" id="havewepone4" readonly name="f32"
                                value="{{ old('f32') }}" class="form-control" placeholder="التاريخ">
                        </div>
                    </div>
                    <hr>
                    <div style="width: 300px; margin: 0 auto!important">
                        <div class="form-group">
                            {!! NoCaptcha::renderJs() !!}
                            {!! NoCaptcha::display() !!}
                            <span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
                        </div>
                    </div>
                    <div class="text-center">
                        <input type="submit" value="تقديم" class="btn btn-primary">
                    </div>
                </div>
                <div id="noyet" style="display: none;">
                    <h4 class="text-center text-danger">غير متاح حالياً</h4>
                </div>
            </form>
            @endif
        </div>
    </div>
    <!-- JavaScript Files -->
    <script src="{{ asset('public/js/jquery-3.4.1.min.js') }}"></script>
    <script src="{{ asset('public/js/popper.min.js') }}"></script>
    <script src="{{ asset('public/js/datepicker.min.js') }}"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"
        integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous">
    </script>
    <script src="{{ asset('public/js/all.min.js') }}"></script>
    <script src="{{ asset('public/js/cropper.min.js') }}"></script>
    <script src="{{ asset('public/lib/select2/js/select2.full.min.js') }}"></script>
    <script src="{{ asset('public/js/form.js') }}"></script>
    <script>
        $(document).ready(function() {

        $("body [data-searchable='searchable']").select2({
        dir: 'rtl',
        theme: 'bootstrap',
        language: 'ar',
        placeholder: 'اختر...',
        });
        var $modal = $('#CropModal');

        var image = document.getElementById('sample_image');

        var cropper;

        $('#upload_image').change(function(event) {
            var files = event.target.files;
            var thisinput = $(this);
            var done = function(url) {
                image.src = url;
                $modal.modal('show');
                $("#crop").attr('data-upload-image', thisinput.attr('data-type'));
            };

            if (files && files.length > 0) {
                reader = new FileReader();
                reader.onload = function(event) {
                    done(reader.result);
                };
                reader.readAsDataURL(files[0]);
            }
        });

        $modal.on('shown.bs.modal', function() {
            cropper = new Cropper(image, {
                dragMode: 'move',
                aspectRatio: 2.5 / 3.0,
                autoCropArea: 0.65,
                restore: false,
                guides: false,
                center: false,
                highlight: false,
                cropBoxMovable: false,
                cropBoxResizable: false,
                toggleDragModeOnDblclick: false,
            });
        }).on('hidden.bs.modal', function() {
            cropper.destroy();
            cropper = null;
        });

        $('#crop').click(function() {
            var dataUploadImage = $(this).attr('data-upload-image');
            canvas = cropper.getCroppedCanvas({
                width: 400,
                height: 600
            });
            canvas.toBlob(function(blob) {
                url = URL.createObjectURL(blob);
                var reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = function() {
                    var base64data = reader.result;
                    $('#uploaded_image').attr('src', base64data);
                    $("#upload1").val(base64data);

                    $modal.modal('hide');
                };
            });
        });
        $('#Zoom_In').click(function() {
            cropper.zoom(0.1);
        });
        $('#Zoom_Out').click(function() {
            cropper.zoom(-0.1);
        });
        window.forceReload = function () {
            if (!window.fetch) return document.location.reload(true);
            var els = document.getElementsByTagName("*");
            for (var i = 0; i < els.length; i++) {
                var src = "";
                if (els[i].tagName == "A") continue;
                if (!src && els[i].src) src = els[i].getAttribute("src");
                if (!src && els[i].href) src = els[i].getAttribute("href");
                if (!src) continue;
                fetch(src, { cache: "reload" });
            }
            return document.location.reload(true);
        };
        });
    </script>

</body>

</html>