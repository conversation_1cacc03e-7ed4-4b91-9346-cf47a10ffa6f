<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Http\Resources\User as UserResource;
use App\User;

Route::get('/', 'indexController@index')->name('index');
Route::get('news', 'indexController@allnews');
Route::get('news/{getnews}', 'indexController@getnews')->name('singlenews');
Route::get('tenders/{gettender}', 'indexController@gettender');
Route::get('mailrecord/action', 'indexController@action')->name('mailrecord.action');
Route::get('contact-us', 'indexController@contact');
Route::post('contact-us', 'indexController@sendmsg')->name('contactus.sendmsg');
Route::get('form/selectidentity', 'FormController@selectidentity');
// Route::get('form/identity', 'FormController@identity')->name('form.identity');
Route::post('form/selectidentity', 'FormController@loginidentity')->name('form.selectidentity');
Route::post('form/selectidentity/update-form', 'FormController@updateidentity')->name('form.updateidentity');
Route::post('form/selectidentity/update-form/store', 'FormController@storeupdateidentity')->name('form.storeupdateidentity');
Route::post('form/identity/store', 'FormController@store')->name('form.identity.store');
Route::get('/sitemap.xml', 'SitemapController@index');
Route::get('/sitemap.xml/news', 'SitemapController@news');
Route::get('/form/obtain-email', 'obtainEmailController@index')->name('obtain-email');
Route::post('/form/obtain-email', 'obtainEmailController@store')->name('form.obtain-email');
Route::get('/form/identity-almkhtar', 'IdentityalmkhtarController@index')->name('form.identity-almkhtar');
Route::post('/form/identity-almkhtar/store', 'IdentityalmkhtarController@store')->name('form.identity-almkhtar.store');
Route::get('/form/dismissed-politicians', 'DismissedPoliticianController@index')->name('form.dismissed-politicians');
Route::post('/form/dismissed-politicians/store', 'DismissedPoliticianController@store')->name('form.dismissed-politicians.store');

Route::get('/form/complaints-POS', 'indexController@complaintsPOS')->name('complaintsPOS');

// Jobs Route
Route::get('/form/jobs2', 'JobsController@index')->name('form.jobs-index');
Route::post('/form/jobs2/store', 'JobsController@store')->name('form.jobs-store');

Route::get('/library', 'LibraryController@index')->name('library.index');
Route::get('/library/cat/{id}', 'LibraryController@fetchBookByCat')->name('library.fetchBookByCat');

// Kirkuk Provincial Council
Route::get('/kirkuk-provincial-council', 'kirkukProvincialCouncilController@index')->name('kirkukprovincialcouncil.index');
Route::get('/kirkuk-provincial-council/edit/{id}', 'kirkukProvincialCouncilController@edit')->name('kirkukprovincialcouncil.edit');
Route::post('/kirkuk-provincial-council/edit/{id}', 'kirkukProvincialCouncilController@update')->name('kirkukprovincialcouncil.update');
Route::get('/kirkuk-provincial-council/print/{id}', 'kirkukProvincialCouncilController@print')->name('kirkukprovincialcouncil.print');
Route::post('/kirkuk-provincial-council/login-kirkuk-provincial-council', 'kirkukProvincialCouncilController@kirkukprovincialcouncillogin')->name('kirkuk.provincial.council.password');
Route::get('form/kirkuk-provincial-council/identity', 'kirkukProvincialCouncilController@identity')->name('form.kirkukprovincialcouncil.identity');
Route::post('form/kirkuk-provincial-council/identity/store', 'kirkukProvincialCouncilController@store')->name('form.kirkukprovincialcouncil.identity.store');
Route::get('/kirkuk-provincial-council/{date}/{where}', 'kirkukProvincialCouncilController@fetch')->name('kirkukprovincialcouncil.fetch');
Route::get('/kirkuk-provincial-council/searchajax', 'kirkukProvincialCouncilController@searchajax')->name('kirkukprovincialcouncil.searchajax');

// Auth complaints
Auth::routes();

Route::get('/complaints', 'complaintsController@index')->name('complaints');
Route::get('/complaints/sendcomplaints', 'complaintsController@sendComplaints')->name('complaints.sendcomplaints');
Route::post('/complaints/sendcomplaints', 'complaintsController@store')->name('complaints.store');
Route::get('/complaints/{complaints}', 'complaintsController@viewComplaints');
Route::post('/complaints', 'complaintsController@storeComment')->name('comment.add');
