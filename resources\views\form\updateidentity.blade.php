@extends('layout.main')

@section('title', 'تجديد هوية موظفي وباج دخول ديوان محافظة كركوك')

@section('content')


<h5 class="text-center font-alhuraa" style="padding-bottom: 27px;">استمارة اصدار هوية موظفي وباج دخول ديوان محافظة كركوك
</h5>
<hr>
<div class="customLink text-right">
    @if ($errors->any())
    <div class="alert alert-danger text-right" id="dispalyErrorCustom">
        <ul>
            @foreach ($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif
    <div class="row text-center">
        @if ($dentity->f1 == 'موظف ملاك' && empty($dentity->job_number))
        <div class="col-sm-6 font-kufi-regular">
            <form action="{{ route('form.storeupdateidentity') }}" method="post" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="upid" value="{{ $dentity->id }}">
                <input type="hidden" name="howmuchupdate" value="{{ $dentity->howmuchupdate }}">
                {{-- <div class="mb-3">
                    <label for="FormControlFile1" class="form-label">يرجى ارفاق تأييد من القسم المعني للرقم الوظيفي ويجب
                        ان يكون بصيغة JPG</label>
                    <input type="file" class="form-control" required name="updateid" id="FormControlFile1">
                </div> --}}
                <div class="mb-3">
                    <label for="numberjobs" class="form-label">الرقم الوظيفي</label>
                    <input type="text" class="form-control" required name="updatenumjob" id="numberjobs">
                </div>
                <input type="submit" class="btn btn-primary" value="ارسال الطلب">
            </form>
        </div>
        @endif
        <div class="col-sm-4 text-center ">
            <img class="img-thumbnail" width="200" src="{{ asset('public/storage/' . $dentity->f2) }}"
                alt="{{ $dentity->f3 }}">
            <h6 class="font-kufi-regular">{{ $dentity->f3 }}</h6>
            <h6 class="font-kufi-regular">{{ $dentity->f5 }}</h6>
            <h6 class="font-kufi-regular">رقم الهوية : {{ $idnumber->idn }}</h6>
            <h6 class="font-kufi-regular">رقم الاستمارة : {{ $dentity->id }}</h6>
        </div>
        <div class="col-sm-8 text-end" style="margin-top: 100px">
            {{-- @if ($dentity->is_edit == 1)
            <form action="{{url('form/identity/verfiy.php')}}" method="post">
                @csrf
                <input type="hidden" value="{{ $dentity->f10 }}" name="numberphone">
                <input type="submit" class="btn btn-primary" value="تعديل بيانات الاستمارة">
            </form>
            @endif --}}
        </div>
    </div>
</div>
@endsection