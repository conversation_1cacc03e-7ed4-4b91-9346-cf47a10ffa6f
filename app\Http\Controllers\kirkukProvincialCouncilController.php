<?php

namespace App\Http\Controllers;

use App\Http\Requests\IdentityRequest2;
use App\Identity2;
use App\Idnumber2;
use App\StatusForm;
use Illuminate\Http\Request;
use Carbon\Carbon;

class kirkukProvincialCouncilController extends Controller
{
    public function index()
    {

        $identitys = Identity2::where('delete_status', 0)->get();
        return view('kirkukprovincialcouncil.index', ['identitys' => $identitys]);
    }

    public function kirkukprovincialcouncillogin(Request $request)
    {
        if ($request->password == 'ata100A') {
            $sessionpass = session()->put('kirkukprovincialcouncilpassword', true);
            return redirect()->back()->with($sessionpass);
        } else {
            return redirect()->back()->with('errorkirkukprovincialcouncilpassword', 'كلمة السر خاطئة!');
        }
    }
    public function identity()
    {
        $statusform = StatusForm::first();
        if ($statusform->statusidentities == 0) {
            return view('form.kirkukprovincialcouncil.identity');
        } else {
            return view('statusform');
        }
    }
    public function edit($id)
    {
        if (session('kirkukprovincialcouncilpassword') == true) {
            $identity = Identity2::where('delete_status', 0)->where('id', $id)->first();
            // if ($identity->status != 'نقص' && $identity->status != 'قيد المراجعة') {
            //     return abort(404);
            // }
            return view('kirkukprovincialcouncil.edit', ['identity' => $identity]);
        } else {
            return redirect()->route('kirkukprovincialcouncil.index');
        }
    }
    public function print($id)
    {
        if (session('kirkukprovincialcouncilpassword') == true) {
            $identity = Identity2::where('delete_status', 0)->where('id', $id)->first();
            if ($identity) {
                $idnumber2 = Idnumber2::where('identities_id', $id)->first();
                $getidnumber = '';

                if ($idnumber2) {
                    if ($idnumber2->idn < 1000 && $idnumber2->idn >= 100) {
                        $getidnumber2 = '0' . $idnumber2->idn;
                    } elseif ($idnumber2->idn < 100) {
                        $getidnumber2 = '00' . $idnumber2->idn;
                    } else {
                        $getidnumber2 = $idnumber2->idn;
                    }
                    $getidnumber = $identity->category . '-' . $getidnumber2;
                }
                return view('kirkukprovincialcouncil.print', ['identity' => $identity, 'getidnumber' => $getidnumber]);
            } else {
                abort(404);
            }
        } else {
            return redirect()->route('kirkukprovincialcouncil.index');
        }
    }
    public function update(Request $request, $id)
    {
        if (session('kirkukprovincialcouncilpassword') == true) {

            if ($request->f21 == null) {
                $f21 = $request->oldf21;
            } else {
                $f21 = $request->f21->store('/uploads/identity', 'public');
            }
            if ($request->f28 == null) {
                $f28 = $request->oldf28;
            } else {
                $f28 = $request->f28->store('/uploads/identity', 'public');
            }
            if ($request->f33 == null) {
                $f33 = $request->oldf33;
            } else {
                $f33 = $request->f33->store('/uploads/identity', 'public');
            }

            $f2Name = $request->f2;
            if (!empty($f2Name)) {
                $image_array_1 = explode(";", $f2Name);
                $image_array_2 = explode(",", $image_array_1[1]);
                $f2Name = base64_decode($image_array_2[1]);

                $f2Upload = rand(0, 10000000) . '_' . time() . '.png';

                $f2 = 'uploads/identity/' . $f2Upload;

                file_put_contents("public/storage/" . $f2, $f2Name);

                // Remove Bg
                $url = "https://api.remove.bg/v1.0/removebg";
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'x-api-key:uJwgfy1zCeatcdguLzPC1iXm',
                ]);

                // move image_url here:
                curl_setopt($ch, CURLOPT_POSTFIELDS, [
                    'image_url' => 'https://kirkuk.gov.iq/public/storage/' . $f2,
                ]);

                $server_output = curl_exec($ch);
                curl_close($ch);

                $fp = fopen("public/storage/" . $f2, "wb");
                fwrite($fp, $server_output);
            } else {
                $f2 = $request->oldf2;
            }


            $f4Ubbercase = strtoupper($request->f4);
            Identity2::where('id', $id)
                ->update(
                    [
                        'f1'    => $request->f1,
                        'f2'    => $f2,
                        'f4'    => $f4Ubbercase,
                        'f5'    => $request->f5,
                        'ff4'    => $request->ff4,
                        'ff5'    => $request->ff5,
                        'ff7'    => $request->ff7,
                        'ff8'    => $request->ff8,
                        'ff9'    => $request->ff9,
                        'f6'    => $request->f6,
                        'f1new'    => $request->f1new,
                        'f2new'    => $request->f2new,
                        'f3new'    => $request->f3new,
                        'f4new'    => $request->f4new,
                        'f5new'    => $request->f5new,
                        'f6new'    => $request->f6new,
                        'f11'    => $request->f11,
                        'f12'    => $request->f12,
                        'f13'    => $request->f13,
                        'f14'    => $request->f14,
                        'f15'    => $request->f15,
                        'f16'    => $request->f16,
                        'f17'    => $request->f17,
                        'f18'    => $request->f18,
                        'f19'    => $request->f19,
                        'addf1'    => $request->addf1,
                        'addf2'    => $request->addf2,
                        'f20'    => $request->f20,
                        'f21'    => $f21,
                        'f22'    => $request->f22,
                        'f23'    => $request->f23,
                        'f24'    => $request->f24,
                        'f25'    => $request->f25,
                        'f26'    => $request->f26,
                        'f27'    => $request->f27,
                        'f28'    => $f28,
                        'f29'    => $request->f29,
                        'f30'    => $request->f30,
                        'f31'    => $request->f31,
                        'f32'    => $request->f32,
                        'job_number'    => $request->job_number,
                        'date_status'    => Carbon::now(),
                        'f33'    => $f33,
                        'status' => 'تجديد',
                    ]
                );
            return redirect('kirkuk-provincial-council');
        } else {
            return redirect()->route('kirkukprovincialcouncil.index');
        }
    }
    public function store(IdentityRequest2 $request)
    {
        $this->validate(
            $request,
            [
                'f1'      => 'required',
                'f3'      => 'required|unique:identities2',
                'f4'      => 'required',
                'f10'      => 'required|unique:identities2',
                'f13'      => 'required',
                'f14'      => 'required',
                'f15'      => 'required',
                'f16'      => 'required',
                'f17'      => 'required',
                'f18'      => 'required',
                'f19'      => 'required',
                'f21'      => 'mimes:png,jpg,jpeg|max:512',
                'f28'      => 'mimes:png,jpg,jpeg|max:512',
                'f33'      => 'mimes:png,jpg,jpeg,gif,pdf|max:512',
                'g-recaptcha-response' => 'required|captcha',
            ]
        );
        $u_agent = \Request::userAgent();
        $bname = 'Unknown';
        $platform = 'Unknown';
        $version = "";

        //First get the platform?
        if (preg_match('/linux/i', $u_agent)) {
            $platform = 'linux';
        } elseif (preg_match('/macintosh|mac os x/i', $u_agent)) {
            $platform = 'mac';
        } elseif (preg_match('/windows|win32/i', $u_agent)) {
            $platform = 'windows';
        }

        // Next get the name of the useragent yes seperately and for good reason
        if (preg_match('/MSIE/i', $u_agent) && !preg_match('/Opera/i', $u_agent)) {
            $bname = 'Internet Explorer';
            $ub = "MSIE";
        } elseif (preg_match('/Firefox/i', $u_agent)) {
            $bname = 'Mozilla Firefox';
            $ub = "Firefox";
        } elseif (preg_match('/Chrome/i', $u_agent)) {
            $bname = 'Google Chrome';
            $ub = "Chrome";
        } elseif (preg_match('/Safari/i', $u_agent)) {
            $bname = 'Apple Safari';
            $ub = "Safari";
        } elseif (preg_match('/Opera/i', $u_agent)) {
            $bname = 'Opera';
            $ub = "Opera";
        } elseif (preg_match('/Netscape/i', $u_agent)) {
            $bname = 'Netscape';
            $ub = "Netscape";
        }

        // finally get the correct version number
        $known = array('Version', $ub, 'other');
        $pattern = '#(?<browser>' . join('|', $known) .
            ')[/ ]+(?<version>[0-9.|a-zA-Z.]*)#';
        if (!preg_match_all($pattern, $u_agent, $matches)) {
            // we have no matching number just continue
        }

        // see how many we have
        $i = count($matches['browser']);
        if ($i != 1) {
            //we will have two since we are not using 'other' argument yet
            //see if version is before or after the name
            if (strripos($u_agent, "Version") < strripos($u_agent, $ub)) {
                $version = $matches['version'][0];
            } else {
                $version = $matches['version'][1];
            }
        } else {
            $version = $matches['version'][0];
        }

        // check if we have a number
        if ($version == null || $version == "") {
            $version = "?";
        }

        if ($request->f21 == null) {
            $f21 = "";
        } else {
            $f21 = $request->f21->store('/uploads/identity', 'public');
        }
        if ($request->f28 == null) {
            $f28 = "";
        } else {
            $f28 = $request->f28->store('/uploads/identity', 'public');
        }
        if ($request->f33 == null) {
            $f33 = "";
        } else {
            $f33 = $request->f33->store('/uploads/identity', 'public');
        }

        $f2Name = $request->f2;
        $image_array_1 = explode(";", $f2Name);
        $image_array_2 = explode(",", $image_array_1[1]);
        $f2Name = base64_decode($image_array_2[1]);

        $f2Upload = rand(0, 10000000) . '_' . time() . '.png';

        $f2 = 'uploads/identity/' . $f2Upload;

        file_put_contents("public/storage/" . $f2, $f2Name);

        // Remove Bg
        $url = "https://api.remove.bg/v1.0/removebg";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'x-api-key:uJwgfy1zCeatcdguLzPC1iXm',
        ]);

        // move image_url here:
        curl_setopt($ch, CURLOPT_POSTFIELDS, [
            'image_url' => 'https://kirkuk.gov.iq/public/storage/' . $f2,
        ]);

        $server_output = curl_exec($ch);
        curl_close($ch);

        $fp = fopen("public/storage/" . $f2, "wb");
        fwrite($fp, $server_output);

        $f4Ubbercase = strtoupper($request->f4);
        Identity2::create([
            'f1'    => $request->f1,
            'f2'    => $f2,
            'f3'    => $request->f3,
            'f4'    => $f4Ubbercase,
            'f5'    => $request->f5,
            'ff4'    => $request->ff4,
            'ff5'    => $request->ff5,
            'ff6'    => $request->ff6,
            'ff7'    => $request->ff7,
            'fff7'    => $request->fff7,
            'ffff7'    => $request->ffff7,
            'ff8'    => $request->ff8,
            'ff9'    => $request->ff9,
            'f6'    => $request->f6,
            'f1new'    => $request->f1new,
            'f2new'    => $request->f2new,
            'f3new'    => $request->f3new,
            'f4new'    => $request->f4new,
            'f5new'    => $request->f5new,
            'f6new'    => $request->f6new,
            'f10'    => $request->f10,
            'f11'    => $request->f11,
            'f12'    => $request->f12,
            'f13'    => $request->f13,
            'f14'    => $request->f14,
            'f15'    => $request->f15,
            'f16'    => $request->f16,
            'f17'    => $request->f17,
            'f18'    => $request->f18,
            'f19'    => $request->f19,
            'f20'    => $request->f20,
            'f21'    => $f21,
            'f22'    => $request->f22,
            'f23'    => $request->f23,
            'f24'    => $request->f24,
            'f25'    => $request->f25,
            'f26'    => $request->f26,
            'f27'    => $request->f27,
            'f28'    => $f28,
            'f29'    => $request->f29,
            'f30'    => $request->f30,
            'f31'    => $request->f31,
            'f32'    => $request->f32,
            'job_number'    => $request->job_number,
            'date_status'    => Carbon::now(),
            'f33'    => $f33,
            'IP'    => \Request::ip(),
            'BrowserAndReports'    => $bname . ' ' . $version . ' ' . $u_agent,


        ]);
        return redirect()->back()->with('successsendidenf', 'تم استلام طلبكم بنجاح سوف تصلكم رسالة نصية على رقم هاتفكم المسجل لدينا تتضمن رقم الاستمارة من خلاله يمكنكم متابعة سير الطلب، كما نحثكم الدخول بين فترة وأخرى الى متابعة سير الطلب عن طريق ادخال اسمكم ورقم الاستمارة ربما يوجد في استمارتكم نقص لكي تتمكنوا من تحديث بياناتها. شاكرين تعاونكم معنا، مع تحيات البوابة الإلكترونية ديوان محافظة كركوك.');
    }

    public function fetch($date, $where)
    {
        if (session('kirkukprovincialcouncilpassword') == true) {
            $fetchdata = Carbon::today();
            if ($date == 'last24hours') {
                $fetchdata = Carbon::today();
            } elseif ($date == 'last7days') {
                $fetchdata = Carbon::today()->subDays(7);
            } elseif ($date == 'last30days') {
                $fetchdata = Carbon::today()->subDays(30);
            } elseif ($date == 'getall') {
                $fetchdata = Carbon::createFromFormat('Y-m-d', '2022-05-1')->toDateTimeString();
            } else {
                $fetchdata = Carbon::today();
            }
            $identitys = Identity2::where('status', str_replace("_", " ", $where))
                ->where('delete_status', 0)
                ->whereDate('date_status', '>=', $fetchdata)
                ->orderBy('id', 'desc')
                ->paginate(5);
            return view('kirkukprovincialcouncil.fetch', ['identitys' => $identitys]);
        } else {
            return redirect()->route('kirkukprovincialcouncil.index');
        }
    }
    public function searchajax(Request $request)
    {
        if (session('kirkukprovincialcouncilpassword') == true) {
            if ($request->ajax()) {
                if ($request->type == 'searchidname') {
                    $identitys = Identity2::where('delete_status', 0)->where('f3', 'like', '%' . $request->inputvalue . '%')
                        ->orwhere('id', 'like', '%' . $request->inputvalue . '%')
                        ->get();
                } elseif ($request->type == 'searchidnumber') {
                    $Idnumber2 = Idnumber2::where('idn', $request->inputvalueidnumber)->first();
                    $identitys = Identity2::where('delete_status', 0)->where('id', $Idnumber2->identities_id)->get();
                }
                $output = '<table class="table table-bordered table-hover text-center" style="font-size: 12px;">
                    <thead class="bg-dark text-white">
                        <tr class="align-middle"> 
                            <th scope="col">التاريخ</th>
                            <th scope="col">رقم الطلب</th>
                            <th scope="col">الاسم</th>
                            <th scope="col">الادارة</th>
                        </tr>
                    </thead>
                    <tbody>';
                foreach ($identitys as $identity) {
                    $output .= '<tr class="align-middle">
                                <th>' . date('Y-m-d', strtotime($identity->created_at)) . '</th>
                                <th>' . $identity->id . '</th>
                                <th>' . $identity->f3 . '</th>
                                <th>';
                    $output .= ' <a href="' . route('kirkukprovincialcouncil.edit', $identity->id) . '" class="btn btn-info btn-sm" title="تجديد"><i class="fas fa-sync-alt"></i></a>';
                    $output .= ' <a target="_blank" href="' . route('kirkukprovincialcouncil.print', $identity->id) . '" class="btn btn-primary btn-sm" title="طباعة"><i class="fas fa-print"></i></a>';

                    $output .= '</th>
                            </tr>';
                }
                $output .= '</tbody></table>';
                return $output;
            }
        } else {
            return redirect()->route('kirkukprovincialcouncil.index');
        }
    }
}
