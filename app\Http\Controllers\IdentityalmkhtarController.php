<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Identityalmkhtar;

class IdentityalmkhtarController extends Controller
{
    public function index()
    {
        return view('form.identity-almkhtar.identityalmkhtar');
    }
    public function store(Request $request)
    {



        $this->validate($request, [
            'fullname'                => 'required|unique:identityalmkhtar,fullname',
            'goverment'               => 'required',
            'dateofbirth'             => 'required',
            'bloodtype'               => 'required',
            'phonenumber'             => 'required|digits:11|unique:identityalmkhtar,phone_number',
            'codenumberidentity'      => 'required|unique:identityalmkhtar,code_number_identity',
            'numberofstamp'           => 'required|unique:identityalmkhtar,number_of_stamp',
            'elimination'             => 'required',
            'side'                    => 'required',
            'village'                 => 'required|unique:identityalmkhtar,village',
            'prefixvillage'           => 'required',
            // 'photo'                   => 'required|mimes:png,jpg,jpeg|max:512',
            'g-recaptcha-response' => 'required|captcha',
        ], [
            'fullname.required' => 'الاسم الرباعي مطلوب',
            'fullname.unique' => 'هذا الاسم مسجل مسبقاً!',
            'goverment.required' => 'يرجى اختيار المحافظة',
            'dateofbirth.required' => 'تاريخ الميلاد مطلوب',
            'bloodtype.required' => 'فصيلة الدم مطلوب',
            'phonenumber.required' => 'رقم الهاتف مطلوب',
            'phonenumber.digits' => 'رقم الهاتف يجب ان يكون 11 رقم',
            'phonenumber.unique' => 'رقم الهاتف مسجل من قبل! حاول ادخال رقم هاتف غير مسجل',
            'codenumberidentity.required' => 'رمز ورقم الهوية مطلوب',
            'codenumberidentity.unique' => 'رمز ورقم الهوية مسجل من قبل! حاول ادخال رمز ورقم الهوية غير مسجل',
            'numberofstamp.required' => 'رمز ورقم الختم مطلوب',
            'numberofstamp.unique' => 'رمز ورقم الختم مسجل من قبل! حاول ادخال رمز ورقم الختم غير مسجل',
            'elimination.required' => 'القضاء مطلوب',
            'side.required' => 'الناحية مطلوب',
            'village.required' => 'القرية او المحلة مطلوبة',
            'village.unique' => 'القرية او المحلة مسجلة من قبل! حاول ادخال قرية او محلة غير مسجلة',
            'prefixvillage.required' => 'الحقل مطلوب',
            // 'photo.required' => 'الصورة الشخصية مطلوب',
            // 'photo.mimes' => 'صيغه الصورة غير معروفه الامتدادات المسموحة (png,jpg,jpeg)',
            // 'photo.max' => 'يجب ان يكون حجم الصورة كحد اقصى 500 كيلوبايت',
            'g-recaptcha-response.required' => 'التحقق مطلوب.',
            'g-recaptcha-response.captcha' => 'خطأ في التحقق.',
        ]);


        $f2Name = $request->photo;
        $image_array_1 = explode(";", $f2Name);
        $image_array_2 = explode(",", $image_array_1[1]);
        $f2Name = base64_decode($image_array_2[1]);

        $f2Upload = rand(0, 10000000) . '_' . time() . '.png';

        $f2 = 'uploads/identityalmkhtar/' . $f2Upload;

        file_put_contents("public/storage/" . $f2, $f2Name);

        // Remove Bg
        $url = "https://api.remove.bg/v1.0/removebg";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'x-api-key:uJwgfy1zCeatcdguLzPC1iXm',
        ]);

        // move image_url here:
        curl_setopt($ch, CURLOPT_POSTFIELDS, [
            'image_url' => 'https://kirkuk.gov.iq/public/storage/' . $f2,
        ]);

        $server_output = curl_exec($ch);
        curl_close($ch);

        $fp = fopen("public/storage/" . $f2, "wb");
        fwrite($fp, $server_output);


        Identityalmkhtar::create([
            'fullname'                      => $request->fullname,
            'code_number_identity'          => $request->codenumberidentity,
            'date_of_birth'                 => $request->dateofbirth,
            'goverment'                     => $request->goverment,
            'blood_type'                    => $request->bloodtype,
            'elimination'                   => $request->elimination,
            'side'                          => $request->side,
            'village'                       => $request->village,
            'prefixvillage'                 => $request->prefixvillage,
            'number_of_stamp'               => $request->numberofstamp,
            'phone_number'                  => $request->phonenumber,
            'expiration_date'               => '2024-06-01',
            'photo'                         => $f2,
        ]);
        $idMatch = Identityalmkhtar::where('fullname', $request->fullname)->first()->id;
        return redirect()->back()->with('successsendalmkhtar', 'تم استلام طلبكم بنجاح رقم الطلب هو: ( ' . $idMatch . ' )');
    }
}
