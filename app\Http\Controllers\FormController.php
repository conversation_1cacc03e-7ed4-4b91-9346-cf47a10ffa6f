<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\IdentityRequest;
use App\Identity;
use App\Idnumber;
use App\StatusForm;
use Illuminate\Support\Facades\Crypt;
use phpDocumentor\Reflection\Types\Boolean;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class FormController extends Controller
{
	public function identity()
	{
		$statusform = StatusForm::first();
		if ($statusform->statusidentities == 0) {
			return view('form.identity');
		} else {
			return view('statusform');
		}
	}
	public function selectidentity()
	{
		return view('form.selectidentity');
	}
	// public function store(IdentityRequest $request)
	// {
	// 	$this->validate($request, [
	// 		'f1'         => 'required',
	// 		'f2'         => 'required',
	// 		'f3'         => 'required',
	// 		'f4'         => 'required',
	// 		'f10'      	 => 'required',
	// 		'f1new'      => 'required',
	// 		'f2new'      => 'required',
	// 		'f3new'      => 'required',
	// 		'f4new'      => 'required',
	// 		'f5new'      => 'required',
	// 		'f13'      => 'required',
	// 		'f14'      => 'required',
	// 		'f15'      => 'required',
	// 		'f16'      => 'required',
	// 		'f19'      => 'required',
	// 		'f20'      => 'required',
	// 		'f27'      => 'required',
	// 		'g-recaptcha-response' => 'required|captcha',
	// 	]);

	// 	// Request Form

	// 	$f2Name = $request->f2;
	// 	$image_array_1 = explode(";", $f2Name);
	// 	$image_array_2 = explode(",", $image_array_1[1]);
	// 	$f2Name = base64_decode($image_array_2[1]);

	// 	$f2Upload = rand(0, 10000000) . '_' . time() . '.png';

	// 	$f2 = 'uploads/identity/' . $f2Upload;

	// 	file_put_contents("public/storage/" . $f2, $f2Name);

	// 	// Remove Bg
	// 	$url = "https://api.remove.bg/v1.0/removebg";
	// 	$ch = curl_init();
	// 	curl_setopt($ch, CURLOPT_URL, $url);
	// 	curl_setopt($ch, CURLOPT_POST, 1);
	// 	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	// 	curl_setopt($ch, CURLOPT_HTTPHEADER, [
	// 		'x-api-key:uJwgfy1zCeatcdguLzPC1iXm',
	// 	]);

	// 	// move image_url here:
	// 	curl_setopt($ch, CURLOPT_POSTFIELDS, [
	// 		'image_url' => 'https://kirkuk.gov.iq/public/storage/' . $f2,
	// 	]);

	// 	$server_output = curl_exec($ch);
	// 	curl_close($ch);

	// 	$fp = fopen("public/storage/" . $f2, "wb");
	// 	fwrite($fp, $server_output);


	// 	$f4Ubbercase = strtoupper($request->f4);
	// 	Identity::create([
	// 		'f1'	=> $request->f1,
	// 		'f2'	=> $f2,
	// 		'f3'	=> $request->f3,
	// 		'f4'	=> $f4Ubbercase,
	// 		'f5'	=> $request->f5,
	// 		'ff5'	=> $request->ff5,
	// 		'ff6'	=> $request->ff6,
	// 		'ff8'	=> $request->ff8,
	// 		'ff9'	=> $request->ff9,
	// 		'f6'	=> $request->f6,
	// 		'f7'	=> $request->f7,
	// 		'f1new'	=> $request->f1new,
	// 		'f2new'	=> $request->f2new,
	// 		'f3new'	=> $request->f3new,
	// 		'f4new'	=> $request->f4new,
	// 		'f5new'	=> $request->f5new,
	// 		'f6new'	=> $request->f6new,
	// 		'f10'	=> $request->f10,
	// 		'f13'	=> $request->f13,
	// 		'f14'	=> $request->f14,
	// 		'f15'	=> $request->f15,
	// 		'f16'	=> $request->f16,
	// 		'f19'	=> $request->f19,
	// 		'f20'	=> $request->f20,
	// 		'f22'	=> $request->f22,
	// 		'f23'	=> $request->f23,
	// 		'f24'	=> $request->f24,
	// 		'f25'	=> $request->f25,
	// 		'f26'	=> $request->f26,
	// 		'f27'	=> $request->f27,
	// 		'f29'	=> $request->f29,
	// 		'f30'	=> $request->f30,
	// 		'f31'	=> $request->f31,
	// 		'f32'	=> $request->f32,
	// 		'job_number'	=> $request->job_number,
	// 	]);
	// 	fclose($fp);
	// 	return response()->json([
	// 		'status' => 'success',
	// 		'successsend' => 'تم استلام طلبكم بنجاح سوف تصلكم رسالة نصية على رقم هاتفكم المسجل لدينا تتضمن رقم الاستمارة من خلاله يمكنكم متابعة سير الطلب، كما نحثكم الدخول بين فترة وأخرى الى متابعة سير الطلب عن طريق ادخال اسمكم ورقم الاستمارة ربما يوجد في استمارتكم نقص لكي تتمكنوا من تحديث بياناتها. شاكرين تعاونكم معنا، مع تحيات البوابة الإلكترونية ديوان محافظة كركوك.'
	// 	]);
	// }

	public function loginidentity(Request $request)
	{
		if (!isset(Identity::where(['f3' => $request->fullname, 'id' => $request->numberform])->first()->id)) {
			return view('form/selectidentity');
		}
		if ($request->fullname == Identity::where('id', $request->numberform)->first()->f3 && $request->numberform == Identity::where('id', $request->numberform)->first()->id) {

			$getIde = Identity::where(['f3' => $request->fullname, 'id' => $request->numberform])->first();
			return view('form/selectidentity')->with('getIde', $getIde);
		} else {
			return view('form/selectidentity');
		}
	}

	public function updateidentity(Request $request)
	{

		$idnumber = Idnumber::where('idn', $request->numberid)->first();

		$dentity = Identity::where(['id' => $idnumber->identities_id, 'f3' => $request->fullname])->first();
		if (!empty($dentity->status)) {
			if ($dentity->status != 'تجديد') {
				if ($dentity) {
					return view('form/updateidentity')->with(['dentity' => $dentity, 'idnumber' => $idnumber]);
				} else {
					return redirect()->back()->with('errorgetident', 'خطأ في الاسم الثلاثي او رقم الهوية');
				}
			} else {
				return redirect()->back()->with('errorhaveupdate', 'لقد تم ارسال طلب التجديد مسبقاً!');
			}
		} else {
			return redirect()->back()->with('errorhaveupdate', 'خطأ في الاسم الثلاثي او رقم الهوية!');
		}
	}
	public function storeupdateidentity(Request $request)
	{
		$this->validate($request, [
			'updatenumjob'      => 'required',
		], [
			'updatenumjob.required'       => 'يرجى كتابة الرقم الوظيفي',
		]);
		Identity::where('id', $request->upid)
			->update(
				[
					'job_number'	=> $request->updatenumjob,
					'howmuchupdate' => $request->howmuchupdate + 1,
					'status' => 'تجديد',
					'update_identity' => now()
				]
			);
		DB::delete('delete from identitiesfile where iden_id = ?', [$request->upid]);
		session()->flash('successsendid', 'تم الارسال بنجاح');
		return view('form/selectidentity');
	}
}
