<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\CatLibrary;
use App\BookLibrary;
class LibraryController extends Controller
{
    public function index()
    {
        $categories = CatLibrary::all()->sortByDesc('id');
        $parents = CatLibrary::where('parent', '>', 0)->orderBy('id', 'desc')->get();
        return view('library.library', ['categories' => $categories, 'parents' => $parents]);
    }
    public function fetchBookByCat($id)
    {
        $books = BookLibrary::where('parent_id', $id)->orderBy('id', 'desc')->get();
        return view('library.fetchbooksbycat', ['books' => $books]);
    }
}
