@extends('layout.main')

@section('title', $complaints->subject)

@section('content')
<div class="text-center">
	<a href="{{ url('complaints') }}" class="btn btn-info">الرجوع الى الشكاوي</a>
</div>
<div class="text-right font-kufi-regular" style="padding: 10px;">
	<br />
	<hr>
	<h6 class="font-kufi-regular"><strong>رقم الشكوى</strong> : {{ $complaints->id }}</h6>
	<small class="text-danger">رقم الشكوى مهم يجب الاحتفاظ به</small>
	<hr>
	<h6 class="font-kufi-regular"><strong>موضوع الشكوى</strong> : {{ $complaints->subject }}</h6>
	<hr>
	<h6 class="font-kufi-regular"><strong>المرفقات</strong> :<br /></h6>
	<table class="table table-striped table-bordered text-center" style="max-width: 500px">
		<thead>
			<tr>
				<th scope="col">المرفق 1</th>
				<th scope="col">المرفق 2</th>
			</tr>
		</thead>
		<tbody>
			<tr>
				<td style="vertical-align: middle;">
					@if (!empty($complaints->file1))
					<a data-fancybox="gallery" href="{{ asset('public/storage/'.$complaints->file1) }}"><img class="img-thumbnail" style="padding:15px; margin:5px" src="{{ asset('public/storage/'.$complaints->file1) }}" width="100px"></a>
					@else
					لايوجد
					@endif
				</td>
				<td style="vertical-align: middle;">
					@if (!empty($complaints->file2))
					<a data-fancybox="gallery" href="{{ asset('public/storage/'.$complaints->file2) }}"><img class="img-thumbnail" style="padding:15px; margin:5px" src="{{ asset('public/storage/'.$complaints->file2) }}" width="100px"></a>
					@else
					لايوجد
					@endif
				</td>
			</tr>
		</tbody>
	</table>
	<hr>
	<p class="text-justify"><strong>نص الشكوى</strong> :<br /> {{ $complaints->content }}</p>
	<hr>
	@if ($actioncomp->count() != 0)
	<h6><strong>الاجراء:</strong></h6>
	@foreach ($actioncomp as $gAction)
	<div style="margin-right: 30px;">
		<div class="alert alert-warning" role="alert">
			<h6><strong>الاجراء المتخذ</strong> :</h6>
			<p>{{ $gAction->action }}</p>
			<p>{{ $gAction->date }}</p>
		</div>
	</div>
	<hr>
	@endforeach
	@endif
	@if ($comments->count() != 0)
	<h6><strong>التعليقات:</strong></h6>
	@foreach ($comments as $gComment)
	@if ($gComment->user_id == 1)
	<div style="margin-right: 30px;">
		<div class="alert alert-secondary" role="alert">
			<h6><strong>ادارة الموقع</strong> :</h6>
			<p>{{ $gComment->comment }}</p>
			<p>{{ $gComment->created_at->format('d-m-Y') }}</p>
		</div>
	</div>
	<hr>
	@else
	<div style="margin-right: 30px;">
		<div class="alert alert-info" role="alert">
			<h6 class="font-kufi-regular"><strong>{{ Auth::user()->name }}</strong> :</h6>
			<p>{{ $gComment->comment }}</p>
			<p>{{ $gComment->created_at->format('d-m-Y') }}</p>
		</div>
	</div>
	<hr>
	@endif
	@endforeach
	@endif
	@if ($complaints->statuscomment == 0)
	@if (session()->has('successcomment'))
	<div class="alert alert-success text-center">
		{!! session('successcomment') !!}
	</div>
	@endif
	<form action="{{ route('comment.add') }}" method="POST">
		@csrf
		<input type="hidden" name="comid" value="{{ $complaints->id }}">
		<div class="form-group" style="width: 500px; margin: auto; padding-bottom: 5px;">
			<label>اضافة تعليق</label>
			<textarea class="@error('comment') is-invalid @enderror form-control" name="comment"></textarea>
		</div>
		@error('comment')
		<div class="text-center">
			<span class="text-danger">{{ $message }}</span>
		</div>
		@enderror
		<div class="text-center">
			<button type="submit" class="btn btn-primary btn-sm">اضافة تعليق</button>
		</div>
	</form>
	<hr>
	@endif
	<div class="text-center">
		<a href="{{ url('complaints') }}" class="btn btn-info">الرجوع الى الشكاوي</a>
	</div>
</div>
@endsection