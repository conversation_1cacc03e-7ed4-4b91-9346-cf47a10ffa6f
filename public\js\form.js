$(function () {
    "use strict";

    $('[data-toggle="datepicker"]').datepicker({
        format: "yyyy-mm-dd",
        months: [
            "كانون الثاني",
            "شباط",
            "آذار",
            "نيسان",
            "أيار",
            "حزيران",
            "تموز",
            "آب",
            "أيلول",
            "تشرين الأول",
            "تشرين الثاني",
            "كانون الأول",
        ],
        monthsShort: [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
        ],
    });

    $(".changestatus").on("change", function () {
        $("#hiddenstatus2").html("");
        $(".hiddeninputs-652hg76GHF6 input").attr("required", "required");
        if ($(this).val() == "عضو مجلس" || $(this).val() == "تابع عضو") {
            $(".hiddeninputs").css("display", "none");
            $(".hiddeninputs select").removeAttr("required");
            $(".hiddeninputs input").removeAttr("required");

            $(".hiddeninputs-652hg76GHF6").css("display", "none");
            $(".hiddeninputs-652hg76GHF6 input").removeAttr("required");
            if ($(this).val() == "عضو مجلس") {
                $(".hiddeninputs-652hg76GHF6").removeAttr("style");
                $(".hiddeninputs-652hg76GHF6 input").attr(
                    "required",
                    "required"
                );
                var html =
                    '<label class="col-sm-3 col-form-label">الوصف الوظيفي</label>' +
                    '<div class="col-sm-4">' +
                    '<select name="fff7" class="form-control" required>' +
                    '<option value="">اختر...</option>' +
                    '<option value="رئيس مجلس محافظة كركوك">رئيس مجلس محافظة كركوك</option>' +
                    '<option value="عضو مجلس محافظة كركوك">عضو مجلس محافظة كركوك</option>' +
                    '<option value="نائب رئيس مجلس المحافظة">نائب رئيس مجلس المحافظة</option>' +
                    '<option value="مقرر مجلس محافظة كركوك">مقرر مجلس محافظة كركوك</option>' +
                    "</select>" +
                    "</div>";
            } else {
                var html =
                    '<label class="col-sm-3 col-form-label">تابع عضو؟</label>' +
                    '<div class="col-sm-4">' +
                    '<select name="ffff7" class="form-control" required>' +
                    '<option value="">اختر...</option>' +
                    '<option value="محمد أبراهيم الحافظ ">محمد أبراهيم الحافظ </option>' +
                    '<option value="أحمد رمزي كريم أحمد">أحمد رمزي كريم أحمد</option>' +
                    '<option value="أحمد عبد الواحد أمين قاسم">أحمد عبد الواحد أمين قاسم</option>' +
                    '<option value="أحمد فاتح مصطفى أحمد">أحمد فاتح مصطفى أحمد</option>' +
                    '<option value="انجيل زيا شيبا زيا">انجيل زيا شيبا زيا</option>' +
                    '<option value="بروين فاتح حميد عارف">بروين فاتح حميد عارف</option>' +
                    '<option value="حسن مجيد رشيد حسين">حسن مجيد رشيد حسين</option>' +
                    '<option value="راكان سعيد علي الجبوري">راكان سعيد علي الجبوري</option>' +
                    '<option value="رعد صالح حسين موسى">رعد صالح حسين موسى</option>' +
                    '<option value="سلوى احمد ميدان حسين">سلوى احمد ميدان حسين</option>' +
                    '<option value="سوسن عبد الواحد شاكر جدوع">سوسن عبد الواحد شاكر جدوع</option>' +
                    '<option value="شوخان حسيب حسين عبد الله">شوخان حسيب حسين عبد الله</option>' +
                    '<option value="ظاهر أنور عاصي حسين">ظاهر أنور عاصي حسين</option>' +
                    '<option value="عبدالله مير ويس الشيخاني">عبدالله مير ويس الشيخاني</option>' +
                    '<option value="نشات شاهويز خورشيد علي">نشات شاهويز خورشيد علي</option>' +
                    '<option value="هوشيار هجران نجم الدين خليل">هوشيار هجران نجم الدين خليل</option>' +
                    "</select>" +
                    "</div>";
                var html2 =
                    '<label class="col-sm-3 col-form-label">الوصف الوظيفي</label>' +
                    '<div class="col-sm-4">' +
                    '<select name="fff7" class="form-control" required>' +
                    '<option value="">اختر...</option>' +
                    '<option value="مدير مكتب">مدير مكتب</option>' +
                    '<option value="سكرتير">سكرتير</option>' +
                    '<option value="مكتب">مكتب</option>' +
                    '<option value="مرافق">مرافق</option>' +
                    '<option value="حماية">حماية</option>' +
                    '<option value="اعلامي">اعلامي</option>' +
                    '<option value="سائق">سائق</option>' +
                    "</select>" +
                    "</div>";
            }

            $("#hiddenstatus").html(html);
            $("#hiddenstatus2").html(html2);
        } else {
            var html =
                '<label class="col-sm-3 col-form-label">العنوان الوظيفي</label>' +
                '                        <div class="col-sm-4">' +
                '<select class="display-results" data-searchable="searchable" name="ff7" required' +
                ' style="width: 100%;">' +
                '<option value="">اختر...</option>' +
                '<option value="موظف عقد">موظف عقد</option>' +
                '<option value="خبير هندسي">خبير هندسي</option>' +
                '<option value="رئيس مهندين اقدم">رئيس مهندين اقدم</option>' +
                '<option value="رئيس مهندسين">رئيس مهندسين</option>' +
                '<option value="معاون رئيس مهندسين">معاون رئيس مهندسين</option>' +
                '<option value="مهندس اقدم">مهندس اقدم</option>' +
                '<option value="مهندس">مهندس</option>' +
                '<option value="م. مهندس">م. مهندس</option>' +
                '<option value="خبير معاون مدير عام">خبير معاون مدير عام</option>' +
                '<option value="مدير اقدم">مدير اقدم</option>' +
                '<option value="مدير">مدير</option>' +
                '<option value="معاون مدير">معاون مدير</option>' +
                '<option value="رئيس ملاحظين">رئيس ملاحظين</option>' +
                '<option value="ملاحظ">ملاحظ</option>' +
                '<option value="م. ملاحظ">م. ملاحظ</option>' +
                '<option value="كاتب">كاتب</option>' +
                '<option value="مدير فني اقدم">مدير فني اقدم</option>' +
                '<option value="مدير فني">مدير فني</option>' +
                '<option value="معاون مدير فني">معاون مدير فني</option>' +
                '<option value="رئيس ملاحظين فنيين">رئيس ملاحظين فنيين</option>' +
                '<option value="ملاحظ فني">ملاحظ فني</option>' +
                '<option value="م. ملاحظ فني">م. ملاحظ فني</option>' +
                '<option value="فني">فني</option>' +
                '<option value="مدير حسابات اقدم">مدير حسابات اقدم</option>' +
                '<option value="مدير حسابات">مدير حسابات</option>' +
                '<option value="معاون مدير حسابات">معاون مدير حسابات</option>' +
                '<option value="محاسب اقدم">محاسب اقدم</option>' +
                '<option value="محاسب">محاسب</option>' +
                '<option value="م. محاسب">م. محاسب</option>' +
                '<option value="كاتب حسابات">كاتب حسابات</option>' +
                '<option value="مدير تدقيق اقدم">مدير تدقيق اقدم</option>' +
                '<option value="مدير تدقيق">مدير تدقيق</option>' +
                '<option value="معاون مدير تدقيق">معاون مدير تدقيق</option>' +
                '<option value="مدقق اقدم">مدقق اقدم</option>' +
                '<option value="مدقق">مدقق</option>' +
                '<option value="م. مدقق">م. مدقق</option>' +
                '<option value="كاتب تدقيق">كاتب تدقيق</option>' +
                '<option value="رئيس حرفيين اقدم">رئيس حرفيين اقدم</option>' +
                '<option value="رئيس حرفيين">رئيس حرفيين</option>' +
                '<option value="معاون رئيس حرفيين">معاون رئيس حرفيين</option>' +
                '<option value="حرفي اقدم">حرفي اقدم</option>' +
                '<option value="حرفي اول">حرفي اول</option>' +
                '<option value="حرفي">حرفي</option>' +
                '<option value="م. حرفي">م. حرفي</option>' +
                '<option value="امين صندوق اقدم">امين صندوق اقدم</option>' +
                '<option value="امين صندوق">امين صندوق</option>' +
                '<option value="م. امين صندوق">م. امين صندوق</option>' +
                '<option value="رئيس محصلين">رئيس محصلين</option>' +
                '<option value="محصل اقدم">محصل اقدم</option>' +
                '<option value="محصل">محصل</option>' +
                '<option value="رئيس سواق اقدم">رئيس سواق اقدم</option>' +
                '<option value="رئيس سواق">رئيس سواق</option>' +
                '<option value="م. رئيس سواق">م. رئيس سواق</option>' +
                '<option value="سائق اقدم">سائق اقدم</option>' +
                '<option value="سائق اول">سائق اول</option>' +
                '<option value="سائق ثاني">سائق ثاني</option>' +
                '<option value="سائق ثالث">سائق ثالث</option>' +
                '<option value="مامور بدالة اقدم">مامور بدالة اقدم</option>' +
                '<option value="مامور بدالة">مامور بدالة</option>' +
                '<option value="مشغل بدالة اول">مشغل بدالة اول</option>' +
                '<option value="مشغل بدالة">مشغل بدالة</option>' +
                '<option value="رئيس حراس اقدم">رئيس حراس اقدم</option>' +
                '<option value="رئيس حراس">رئيس حراس</option>' +
                '<option value="م. رئيس حراس">م. رئيس حراس</option>' +
                '<option value="حارس اقدم">حارس اقدم</option>' +
                '<option value="حارس اول">حارس اول</option>' +
                '<option value="حارس ثاني">حارس ثاني</option>' +
                '<option value="حارس ثالث">حارس ثالث</option>' +
                '<option value="رئيس كاتب طابعة">رئيس كاتب طابعة</option>' +
                '<option value="م. رئيس طابعة">م. رئيس طابعة</option>' +
                '<option value="كاتب طابعة اقدم">كاتب طابعة اقدم</option>' +
                '<option value="كاتب طابعة اول">كاتب طابعة اول</option>' +
                '<option value="كاتب طابعة ثاني">كاتب طابعة ثاني</option>' +
                '<option value="كاتب طابعة ثالث">كاتب طابعة ثالث</option>' +
                '<option value="موظف خدمات اقدم">موظف خدمات اقدم</option>' +
                '<option value="موظف خدمات">موظف خدمات</option>' +
                '<option value="م. موظف خدمات">م. موظف خدمات</option>' +
                '<option value="مستشار قانوني">مستشار قانوني</option>' +
                '<option value="مستشار قانوني مساعد">مستشار قانوني مساعد</option>' +
                '<option value="مشاور قانوني اقدم">مشاور قانوني اقدم</option>' +
                '<option value="مشاور قانوني مساعد">مشاور قانوني مساعد</option>' +
                '<option value="قانوني">قانوني</option>' +
                '<option value="م.قانوني">م.قانوني</option>' +
                '<option value="مدير تنفيذي">مدير تنفيذي</option>' +
                '<option value="رئيس مبرمجين اقدم">رئيس مبرمجين اقدم</option>' +
                '<option value="رئيس مبرمجين">رئيس مبرمجين</option>' +
                '<option value="معاون رئيس مبرمجين">معاون رئيس مبرمجين</option>' +
                '<option value="مبرمج اقدم">مبرمج اقدم</option>' +
                '<option value="معاون مبرمج">معاون مبرمج</option>' +
                '<option value="مبرمج">مبرمج</option>' +
                '<option value="رئيس مشغلي حاسبة">رئيس مشغلي حاسبة</option>' +
                '<option value="مشغل حاسبة اقدم">مشغل حاسبة اقدم</option>' +
                '<option value="مشغل حاسبة">مشغل حاسبة</option>' +
                '<option value="رئيس اختصاص نظم معومات اقدم">رئيس اختصاص نظم معومات اقدم</option>' +
                '<option value="رئيس اختصاص نظم معومات">رئيس اختصاص نظم معومات</option>' +
                '<option value="م.رئيس اختصاص نظم معومات">م.رئيس اختصاص نظم معومات</option>' +
                '<option value="اختصاصي نظم معلومات اقدم">اختصاصي نظم معلومات اقدم</option>' +
                '<option value="اختصاصي نظم معلومات">اختصاصي نظم معلومات</option>' +
                '<option value="م. اختصاصي نظم معلومات">م. اختصاصي نظم معلومات</option>' +
                '<option value="رئيس احصائيين اقدم">رئيس احصائيين اقدم</option>' +
                '<option value="رئيس احصائيين">رئيس احصائيين</option>' +
                '<option value="معاون رئيس احصائيين">معاون رئيس احصائيين</option>' +
                '<option value="احصائي اقدم">احصائي اقدم</option>' +
                '<option value="احصائي">احصائي</option>' +
                '<option value="م. احصائي">م. احصائي</option>' +
                '<option value="مدير مخزن اقدم">مدير مخزن اقدم</option>' +
                '<option value="مدير مخازن">مدير مخازن</option>' +
                '<option value="معاون مدير مخازن">معاون مدير مخازن</option>' +
                '<option value="امين مخزن اقدم">امين مخزن اقدم</option>' +
                '<option value="امين مخزن">امين مخزن</option>' +
                '<option value="م. امين مخزن">م. امين مخزن</option>' +
                '<option value="كاتب مخزن">كاتب مخزن</option>' +
                '<option value="رئيس مترجمين اقدم">رئيس مترجمين اقدم</option>' +
                '<option value="رئيس مترجمين">رئيس مترجمين</option>' +
                '<option value="معاون رئيس مترجمين">معاون رئيس مترجمين</option>' +
                '<option value="مترجم اقدم">مترجم اقدم</option>' +
                '<option value="مترجم">مترجم</option>' +
                '<option value="معاون مترجم">معاون مترجم</option>' +
                '<option value="رئيس رسامين هندسي اقدم">رئيس رسامين هندسي اقدم</option>' +
                '<option value="رئيس رسامين هندسي">رئيس رسامين هندسي</option>' +
                '<option value="معاون رئيس رسامين هندسي">معاون رئيس رسامين هندسي</option>' +
                '<option value="رسام هندسي اقدم">رسام هندسي اقدم</option>' +
                '<option value="رسام هندسي">رسام هندسي</option>' +
                '<option value="معاون رسام هندسي">معاون رسام هندسي</option>' +
                '<option value="رئيس مساحين اقدم">رئيس مساحين اقدم</option>' +
                '<option value="رئيس مساحين">رئيس مساحين</option>' +
                '<option value="معاون رئيس مساحين">معاون رئيس مساحين</option>' +
                '<option value="مساح اقدم">مساح اقدم</option>' +
                '<option value="مساح">مساح</option>' +
                '<option value="معاون مساح">معاون مساح</option>' +
                '<option value="رئيس كميائين اقدم">رئيس كميائين اقدم</option>' +
                '<option value="رئيس كميائين">رئيس كميائين</option>' +
                '<option value="معاون رئيس كميائين">معاون رئيس كميائين</option>' +
                '<option value="كيميائي اقدم">كيميائي اقدم</option>' +
                '<option value="م. كيميائي">م. كيميائي</option>' +
                '<option value="رئيس فيزياوي اقدم">رئيس فيزياوي اقدم</option>' +
                '<option value="رئيس فيزياوي">رئيس فيزياوي</option>' +
                '<option value="معاون رئيس فيزياوي">معاون رئيس فيزياوي</option>' +
                '<option value="فيزياوي اقدم">فيزياوي اقدم</option>' +
                '<option value="م.فيزياوي">م.فيزياوي</option>' +
                '<option value="فيزياوي">فيزياوي</option>' +
                '<option value="معاون قضائي سادس">معاون قضائي سادس</option>' +
                '<option value="معاون قضائي خامس">معاون قضائي خامس</option>' +
                '<option value="معاون قضائي رابع">معاون قضائي رابع</option>' +
                '<option value="معاون قضائي ثالث">معاون قضائي ثالث</option>' +
                '<option value="معاون قضائي ثان">معاون قضائي ثان</option>' +
                '<option value="معاون قضائي اول">معاون قضائي اول</option>' +
                '<option value="معاون قضائي اقدم">معاون قضائي اقدم</option>' +
                '<option value="اعلامي">اعلامي</option>' +
                '<option value="لايوجد">لايوجد</option>' +
                "</select>" +
                "</div>";
            $("#hiddenstatus").html(html);
            $("body [data-searchable='searchable']").select2({
                dir: "rtl",
                theme: "bootstrap",
                language: "ar",
                placeholder: "اختر...",
            });
            $(".hiddeninputs").removeAttr("style");
            $(".hiddeninputs input").attr("required", "required");
            $(".hiddeninputs select").attr("required", "required");
        }
    });
});

function yesnoCheck(that) {
    if (that.value == "موظف ملاك") {
        $(".requirejobsselect").removeAttr("required", "required");
        $("#addresJobs").attr("required", "required");
        $("#requirejobnumber").attr("required", "required");
        $("#sectionJobs").attr("required", "required");
        document.getElementById("ifYes").style.display = "";
        document.getElementById("status1").style.display = "none";
        document.getElementById("hiddenjobs").style.display = "none";
        document.getElementById("status2").style.display = "";
        document.getElementById("hiddenwepon").style.display = "none";
        document.getElementById("hiddennameen").style.display = "";
        document.getElementById("noyet").style.display = "none";
        document.getElementById("hiddencat").style.display = "";
        document.getElementById("showcat").style.display = "none";
        document.getElementById("showallgov").style.display = "none";
        document.getElementById("hiddenallgov").style.display = "";
        document.getElementById("hiddenjobnumber").style.display = "";
        document.getElementById("hiddenallcat").style.display = "";
        document.getElementById("logoshow").style.display = "none";
        $("#requireCustom1").attr("required", "required");
        $("#requireCustom55").attr("required", "required");
        $("#requireCustom66").attr("required", "required");
        $("#idNumber").attr("required", "required");
        $("#idNumber1").attr("required", "required");
        $(".requireDate").attr("required", "required");
    } else if (that.value == "باج دخول ديوان محافظة كركوك") {
        $(".requirejobsselect").attr("required", "required");
        $("#requirejobnumber").removeAttr("required", "required");
        $("#addresJobs").removeAttr("required", "required");
        $("#sectionJobs").removeAttr("required", "required");
        document.getElementById("ifYes").style.display = "";
        document.getElementById("status1").style.display = "none";
        document.getElementById("status2").style.display = "";
        document.getElementById("hiddenwepon").style.display = "none";
        document.getElementById("hiddennameen").style.display = "";
        document.getElementById("hiddenjobs").style.display = "";
        document.getElementById("noyet").style.display = "none";
        document.getElementById("hiddencat").style.display = "";
        document.getElementById("showcat").style.display = "none";
        document.getElementById("showallgov").style.display = "";
        document.getElementById("hiddenallgov").style.display = "none";
        document.getElementById("hiddenjobnumber").style.display = "none";
        document.getElementById("hiddenallcat").style.display = "none";
        document.getElementById("logoshow").style.display = "none";
        $("#requireCustom1").attr("required", "required");
        $("#requireCustom55").attr("required", "required");
        $("#requireCustom66").attr("required", "required");
        $(".requireDate").removeAttr("required", "required");
    } else if (that.value == "اخرى") {
        $(".requirejobsselect").attr("required", "required");
        $("#requirejobnumber").removeAttr("required", "required");
        $("#addresJobs").removeAttr("required", "required");
        $("#sectionJobs").removeAttr("required", "required");
        document.getElementById("ifYes").style.display = "";
        document.getElementById("status1").style.display = "none";
        document.getElementById("status2").style.display = "";
        document.getElementById("hiddenwepon").style.display = "";
        document.getElementById("hiddennameen").style.display = "";
        document.getElementById("hiddenjobs").style.display = "";
        document.getElementById("noyet").style.display = "none";
        document.getElementById("hiddencat").style.display = "";
        document.getElementById("showcat").style.display = "none";
        document.getElementById("showallgov").style.display = "";
        document.getElementById("hiddenallgov").style.display = "none";
        document.getElementById("hiddenjobnumber").style.display = "none";
        document.getElementById("hiddenallcat").style.display = "none";
        document.getElementById("logoshow").style.display = "none";
        $("#requireCustom1").attr("required", "required");
        $("#requireCustom55").attr("required", "required");
        $("#requireCustom66").attr("required", "required");
        $(".requireDate").removeAttr("required", "required");
    } else {
        document.getElementById("ifYes").style.display = "none";
        $("#requirejobnumber").removeAttr("required", "required");
        document.getElementById("hiddenjobs").style.display = "none";
        document.getElementById("status1").style.display = "";
        document.getElementById("status2").style.display = "none";
        document.getElementById("noyet").style.display = "none";
        document.getElementById("hiddenwepon").style.display = "none";
        document.getElementById("hiddennameen").style.display = "none";
        document.getElementById("logoshow").style.display = "";
        document.getElementById("dispalyErrorCustom").style.display = "";
        $("#requireCustom1").removeAttr("required", "required");
    }
}

function jobsCheck(that) {
    if (that.value == "نعم") {
        $("#requirejobs1").attr("required", "required");
        $("#requirejobs2").removeAttr("required", "required");
        $("#requirejobs3").removeAttr("required", "required");
        document.getElementById("hiddenjobs2").style.display = "";
        document.getElementById("hiddenjobs3").style.display = "none";
        document.getElementById("hiddenjobs4").style.display = "none";
    } else {
        $("#requirejobs2").attr("required", "required");
        $("#requirejobs3").attr("required", "required");
        $("#requirejobs1").removeAttr("required", "required");
        document.getElementById("hiddenjobs2").style.display = "none";
        document.getElementById("hiddenjobs3").style.display = "";
        document.getElementById("hiddenjobs4").style.display = "";
    }
}

function yesnoCheck2(that) {
    if (that.value == "نعم") {
        document.getElementById("ifYes2").style.display = "";
        document.getElementById("customfile").style.display = "";
        document.getElementById("hiddenNote").style.display = "none";
        $("#haveCar").attr("required", "required");
        $("#haveCar1").attr("required", "required");
        $("#haveCar2").attr("required", "required");
        $("#haveCar3").attr("required", "required");
        $("#haveCar4").attr("required", "required");
        $("#haveCar5").attr("required", "required");
        $("#inputGroupFile01").attr("required", "required");
    } else {
        document.getElementById("ifYes2").style.display = "none";
        document.getElementById("customfile").style.display = "none";
        document.getElementById("hiddenNote").style.display = "";
        $("#haveCar").removeAttr("required", "required");
        $("#haveCar1").removeAttr("required", "required");
        $("#haveCar2").removeAttr("required", "required");
        $("#haveCar3").removeAttr("required", "required");
        $("#haveCar4").removeAttr("required", "required");
        $("#haveCar5").removeAttr("required", "required");
        $("#inputGroupFile01").removeAttr("required", "required");
    }
}

function yesnoCheck3(that) {
    if (that.value == "نعم") {
        document.getElementById("ifYes3").style.display = "";
        document.getElementById("customfile2").style.display = "";
        document.getElementById("hiddenNote2").style.display = "none";
        $("#havewepone1").attr("required", "required");
        $("#havewepone2").attr("required", "required");
        $("#havewepone3").attr("required", "required");
        $("#havewepone4").attr("required", "required");
    } else {
        document.getElementById("ifYes3").style.display = "none";
        document.getElementById("customfile2").style.display = "none";
        document.getElementById("hiddenNote2").style.display = "";
        $("#havewepone1").removeAttr("required", "required");
        $("#havewepone2").removeAttr("required", "required");
        $("#havewepone3").removeAttr("required", "required");
        $("#havewepone4").removeAttr("required", "required");
    }
}

function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) {
            $(".image-upload-wrap").hide();

            $(".file-upload-image").attr("src", e.target.result);
            $(".file-upload-content").show();

            $(".image-title").html(input.files[0].name);
        };

        reader.readAsDataURL(input.files[0]);
    } else {
        removeUpload();
    }
}

function removeUpload() {
    $(".file-upload-input").replaceWith($(".file-upload-input").clone());
    $(".file-upload-content").hide();
    $(".image-upload-wrap").show();
}
$(".image-upload-wrap").bind("dragover", function () {
    $(".image-upload-wrap").addClass("image-dropping");
});
$(".image-upload-wrap").bind("dragleave", function () {
    $(".image-upload-wrap").removeClass("image-dropping");
});

function showMyImage(fileInput) {
    "use strict";
    document.getElementById("thumbnil").style.display = "";
    document.getElementById("showbotton").style.display = "";
    var files = fileInput.files;
    for (var i = 0; i < files.length; i++) {
        var file = files[i];
        var imageType = /image.*/;
        if (!file.type.match(imageType)) {
            continue;
        }
        var img = document.getElementById("thumbnil");
        img.file = file;
        var reader = new FileReader();
        reader.onload = (function (aImg) {
            return function (e) {
                aImg.src = e.target.result;
            };
        })(img);
        reader.readAsDataURL(file);
    }
}
