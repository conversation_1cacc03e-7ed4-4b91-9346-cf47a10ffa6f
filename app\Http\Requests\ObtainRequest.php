<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ObtainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'f1'      => 'required',
            'f2'      => 'required',
            'f3'      => 'required',
            'f6'      => 'required|unique:replaceemail,UserName',
            'f7'      => 'required|digits:11',
            'f10'      => 'mimes:png,jpg,jpeg,gif,pdf|max:512',
            'g-recaptcha-response' => 'required|captcha',
        ];
    }
    public function messages()
    {
        return [
            'f1.required'    => 'حقل اسم الوزارة مطلوب.',
            'f2.required'    => 'حقل اسم الدائرة حسب التشكيل الوزاري مطلوب.',
            'f3.required'    => 'حقل اسم القسم او الشعبه مطلوب.',
            'f6.required'         => 'حقل اسم مستخدم البريد الألكتروني مطلوب.',
            'f6.unique'         => 'اسم المستخدم هذا موجود.',
            'f7.required'    => 'حقل رقم هاتف مستخدم البريد الألكتروني مطلوب.',
            'f7.digits'     => 'يجب ان يكون رقم الهاتف 11 رقم.',
            'f10.mimes'   => 'امتداد الملف غير مدعوم.',
            'f10.max'   => 'يجب ان يكون حجم الملف المرفق اقل من 512KB.',
            'g-recaptcha-response.required' => 'التحقق مطلوب.',
            'g-recaptcha-response.captcha' => 'خطأ في التحقق.',
        ];
    }
}
