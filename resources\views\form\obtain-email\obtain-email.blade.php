@extends('layout.main')

@section('title', 'استمارة استحصال البريد الألكتروني | ديوان محافظة كركوك')

@section('content')

<div class="container">

<h2 class="font-alhuraa text-center">بوابة طلب البريد الالكتروني الحكومي الرسمي</h2>
<p class="ml-4 font-kufi-regular" style="text-align: justify">{{ $obtain }}</p>
<hr class="ml-4">
@if (session()->has('successsendobtain'))
    <div class="alert alert-success text-center ml-4 font-kufi-regular">
        {!! session('successsendobtain') !!}
    </div>
@else
<div class="ml-4 text-right font-kufi-regular">
	<form action="{{ route('form.obtain-email') }}" method="POST" enctype="multipart/form-data">
		@csrf
	  <div class="form-group mb-4">
	    <label for="Input1">اسم الوزارة</label>
	    <input type="text" class="form-control @error('f1') is-invalid @enderror" value="{{ old('f1') }}" id="Input1" name="f1">
        @error('f1')
            <span class="invalid-feedback" role="alert">
                <strong>{{ $message }}</strong>
            </span>
        @enderror
	  </div>
	  <div class="form-group mb-4">
	    <label for="Input2">اسم الدائرة حسب التشكيل الوزاري</label>
	    <input type="text" class="form-control @error('f2') is-invalid @enderror" value="{{ old('f2') }}" id="Input2" name="f2">
	    @error('f2')
	        <span class="invalid-feedback" role="alert">
	            <strong>{{ $message }}</strong>
	        </span>
	    @enderror
	  </div>
	  <div class="form-group mb-4">
	    <label for="Input3">اسم القسم او الشعبه</label>
	    <input type="text" class="form-control @error('f3') is-invalid @enderror" value="{{ old('f3') }}" id="Input3" name="f3">
	    @error('f3')
	        <span class="invalid-feedback" role="alert">
	            <strong>{{ $message }}</strong>
	        </span>
	    @enderror
	  </div>
        <div class="badge-obtin">
            <h6 class="font-kufi-regular">معلومات مدير الدائرة</h6>
        </div>
	  <div class="form-group mb-4">
	    <label for="Input4">اسم مدير الدائرة</label>
	    <input type="text" class="form-control" id="Input4" value="{{ old('f4') }}" name="f4">
	  </div>
	  <div class="form-group mb-4">
	    <label for="Input5">رقم هاتف مدير الدائرة</label>
	    <input type="text" class="form-control" id="Input5" value="{{ old('f5') }}" name="f5">
	  </div>
        <div class="badge-obtin">
            <h6 class="font-kufi-regular">معلومات مستخدم البريد الالكتروني</h6>
        </div>
	  <div class="form-group mb-4">
	    <label for="Input6">اسم مستخدم البريد الألكتروني</label>
	    <input type="text" class="form-control @error('f6') is-invalid @enderror" id="Input6" value="{{ old('f6') }}" name="f6">
	    @error('f6')
	        <span class="invalid-feedback" role="alert">
	            <strong>{{ $message }}</strong>
	        </span>
	    @enderror
	  </div>
	  <div class="form-group mb-4">
	    <label for="Input7">رقم هاتف مستخدم البريد الألكتروني</label>
	    <input type="text" class="form-control @error('f7') is-invalid @enderror" id="Input7" value="{{ old('f7') }}" name="f7">
	    @error('f7')
	        <span class="invalid-feedback" role="alert">
	            <strong>{{ $message }}</strong>
	        </span>
	    @enderror
	  </div>

	  <div class="form-group mb-4">
	    <label for="Input8">حالة البريد الألكتروني</label>
	    <select class="form-control" id="Input8" name="f8">
	    	<option <?php if (old('f8') == 'عام') { echo 'selected'; } ?> value="عام">عام</option>
	    	<option <?php if (old('f8') == 'خاص') { echo 'selected'; } ?> value="خاص">خاص</option>
	    </select>
	  </div>

	  <div class="form-group mb-4">
	    <label for="Input9">اذا كان لديك بريد تخاطب الكتروني يرجى كتابته وان كان لا فأتركه فارغاً</label>
	    <input type="email" class="form-control" id="Input9" name="f9" value="{{ old('f9') }}">
	  </div>

	  <div class="form-group mb-4">
	    <label for="Input10">يرجى تحميل طلب خطي او كتاب معنون الى ديوان محافظة كركوك / شعبة تكنلوجيا المعلومات وفيه موافقة مدير الدائرة او مسؤول القسم او مسؤول الشعبة على استحصال البريد الحكومي</label>
	    <input type="file" class="form-control @error('f10') is-invalid @enderror" id="Input10" name="f10" accept=".png,.jpg,.jpeg,.gif,.pdf" aria-describedby="mimeHelp">
	    <small id="mimeHelp" class="form-text text-muted">الأمتدادات المسموحة: (png,jpg,jpeg,gif,pdf)</small>
	    @error('f10')
	        <span class="invalid-feedback" role="alert">
	            <strong>{{ $message }}</strong>
	        </span>
	    @enderror
	  </div>
	  <hr>
	  <div style="width: 300px; margin: 0 auto!important">
	    <div class="form-group mb-4">
	        {!! NoCaptcha::renderJs() !!}
	        {!! NoCaptcha::display() !!}
	      <span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
	    </div>
	  </div>
	  <div class="text-center">
	  	<button type="submit" class="btn btn-primary">ارسال الطلب</button>
	  </div>
	</form>
</div>
@endif
</div>
@endsection