@extends('layout.mainkirkukcouncil')

@section('title', 'نافذة اصدار هويات - مجلس محافظة كركوك')

@section('content')
@if (session('kirkukprovincialcouncilpassword') == true)
<div class="upbar text-center bg-dark p-2">
    <h4 class="text-white">نافذة اصدار الهويات</h4>
</div>
<div class="mt-3 text-center">
    <a href="{{route('kirkukprovincialcouncil.index')}}" class="btn btn-secondary btnback"><i
            class="fas fa-home fa-2x"></i></a>
</div>
<div class="container">
    <div class="row align-items-center" style="margin-top: 120px">
        <div class="col-sm-6">
            <div class="main-table mt-4">
                <table class="table table-bordered table-hover text-center" style="font-size: 12px;">
                    <thead class="bg-dark text-white">
                        <tr class="align-middle">
                            <th scope="col">التاريخ</th>
                            <th scope="col">رقم الطلب</th>
                            <th scope="col">الاسم</th>
                            <th scope="col">الادارة</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($identitys as $identity)
                        <tr class="align-middle">
                            <th>{{date('Y-m-d', strtotime($identity->created_at))}}</th>
                            <th>{{$identity->id}}</th>
                            <th>{{$identity->f3}}</th>
                            <th>
                                <a href="{{route('kirkukprovincialcouncil.edit', $identity->id)}}"
                                    class="btn btn-info btn-sm" title="تجديد"><i class="fas fa-sync-alt"></i></a>
                                <a target="_blank" href="{{route('kirkukprovincialcouncil.print', $identity->id)}}"
                                    class="btn btn-primary btn-sm" title="طباعة"><i class="fas fa-print"></i></a>

                            </th>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                {{ $identitys->links() }}
            </div>
        </div>
        <div class="col-sm-6">
            <header>
                <img class="img-fluid" src="{{ asset('public/kirkukprovincialcouncil/asset/img/logo.webp') }}"
                    alt="Logo">
            </header>
            <div class="main">
                <div class="mt-5 button-form">
                    <a href="{{ route('form.kirkukprovincialcouncil.identity') }}" target="_blank"
                        class="btn btn-primary">التقديم على استمارة الهويات</a>
                </div>
            </div>
        </div>
    </div>
    @else
    <div class="login">
        <div class="form">
            <form class="login-form" action="{{ route('kirkuk.provincial.council.password') }}" method="POST">
                @csrf

                <input type="password" name="password"
                    class="form-control @if (session('errorkirkukprovincialcouncilpassword')) is-invalid @endif"
                    placeholder="كلمة السر" required />
                @if (session('errorkirkukprovincialcouncilpassword'))
                <div class="invalid-feedback">
                    {{ session('errorkirkukprovincialcouncilpassword') }}
                </div>
                @endif
                <button>دخول</button>
            </form>
        </div>
    </div>
    @endif
    @endsection