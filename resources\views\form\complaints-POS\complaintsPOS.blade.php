@extends('layout.main')

@section('title', 'الملاحظات والمعوقات الخاصة بنظام
الدفع الالكتروني POS | ديوان محافظة كركوك')

@section('content')

<div class="container">

	<h2 class="font-alhuraa text-center">الملاحظات والمعوقات الخاصة بنظام
		الدفع الالكتروني POS</h2>
	<p class="ml-4 font-kufi-regular" style="text-align: justify"></p>
	<hr class="ml-4">
	@if (session()->has('successsendobtain'))
	<div class="alert alert-success text-center ml-4 font-kufi-regular">
		{!! session('successsendobtain') !!}
	</div>
	@else
	<div class="ml-4 text-right font-kufi-regular">
		<form>
			@csrf
			<div class="form-group mb-4">
				<label for="Input1">اسم المواطن</label>
				<input type="text" class="form-control @error('f1') is-invalid @enderror" value="{{ old('f1') }}"
					id="Input1" name="f1">
				@error('f1')
				<span class="invalid-feedback" role="alert">
					<strong>{{ $message }}</strong>
				</span>
				@enderror
			</div>
			<div class="form-group mb-4">
				<label for="Input2">رقم الهاتف</label>
				<input type="text" class="form-control @error('f2') is-invalid @enderror" id="Input2"
					value="{{ old('f2') }}" name="f2">
				@error('f2')
				<span class="invalid-feedback" role="alert">
					<strong>{{ $message }}</strong>
				</span>
				@enderror
			</div>
			<div class="form-group mb-4">
				<label for="Input3">اسم شركة الدفع الالكتروني او اسم بطاقة الماستر كارد الخاصة بالدفع</label>
				<input type="text" class="form-control @error('f3') is-invalid @enderror" id="Input3"
					value="{{ old('f3') }}" name="f3">
				@error('f3')
				<span class="invalid-feedback" role="alert">
					<strong>{{ $message }}</strong>
				</span>
				@enderror
			</div>
			<div class="form-group mb-4">
				<label for="Input4">الملاحظات</label>
				<textarea class="form-control @error('f4') is-invalid @enderror" id="Input4" name="f4" cols="30"
					rows="10"></textarea>
				@error('f4')
				<span class="invalid-feedback" role="alert">
					<strong>{{ $message }}</strong>
				</span>
				@enderror
			</div>
			<hr>
			<div style="width: 300px; margin: 0 auto!important">
				<div class="form-group mb-4">
					{!! NoCaptcha::renderJs() !!}
					{!! NoCaptcha::display() !!}
					<span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
				</div>
			</div>
			<div class="text-center">
				<button type="submit" class="btn btn-primary">ارسال</button>
			</div>
		</form>
	</div>
	@endif
</div>
@endsection