@extends('layout.main')

@section('title', 'ادارة نظام الشكاوي')

@section('content')
@guest
<li class="nav-item">
  <a class="nav-link font-kufi-regular" href="{{ route('login') }}">تسجيل دخول</a>
</li>
@if (Route::has('register'))
<li class="nav-item">
  <a class="nav-link font-kufi-regular" href="{{ route('register') }}">تسجيل مستخدم جديد</a>
</li>
@endif
@else
<li style="cursor: pointer; font-weight: bold;" class="nav-item list-unstyled text-center font-kufi-regular" data-bs-toggle="modal" data-bs-target="#logout">
  @if (date('a') == 'am')
  صباح الخير
  @else
  مساء الخير
  @endif
  {{ Auth::user()->name }}
</li>
<!-- Modal -->
<div class="modal fade" id="logout" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title font-kufi-regular" id="exampleModalLabel">{{ Auth::user()->name }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>

      </div>
      <div class="modal-body font-kufi-regular">
        هل انت متأكد من تسجيل الخروج؟
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary font-kufi-regular" data-bs-dismiss="modal">اغلاق</button>
        <a class="btn btn-primary" href="{{ route('logout') }}" onclick="event.preventDefault();
                                     document.getElementById('logout-form').submit();">
          تسجيل الخروج
        </a>
        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
          @csrf
        </form>
      </div>
    </div>
  </div>
</div>
<!-- End Modal -->

<div class="text-center mt-2 mb-2">
  <a class="btn btn-primary font-kufi-regular" href="{{ route('complaints.sendcomplaints') }}">تقديم شكوى</a>
</div>
<hr style="max-width: 400px; margin:auto;" class="mt-2 mb-2">
<div class="container text-center" style="font-size: 12px; padding-bottom: 50px;">
  @if (session()->has('success'))
  <div class="alert alert-success text-center">
    {!! session('success') !!}
  </div>
  @endif
  @if (App\Complaint::all()->where('user_id', Auth::user()->id)->count() != 0)
  <table class="table table-bordered font-kufi-regular">
    <thead>
      <tr>
        <th scope="col">#</th>
        <th scope="col">التاريخ</th>
        <th scope="col">رقم الشكوى /<span class="text-danger"> مهم</span></th>
        <th scope="col">موضوع الشكوى</th>
        <th scope="col">ادارة</th>
      </tr>
    </thead>
    @foreach ($complaints as $key => $complaint)
    <tbody>
      <tr>
        <td>{{ $key + 1 }}</td>
        <td>{{ $complaint->date->format('Y-m-d g:i A') }}</td>
        <td>{{ $complaint->id }}</td>
        <td>{{ $complaint->subject }}</td>
        <th scope="row"><a class="custom-link" href="{{ url('complaints') . '/' . $complaint->id }}">متابعة</a></th>
      </tr>
    </tbody>

    @endforeach
  </table>
  @else
  <h4 class=" font-kufi-regular">لاتوجد شكاوي حاليا</h4>
  @endif
</div>
@endguest
@endsection