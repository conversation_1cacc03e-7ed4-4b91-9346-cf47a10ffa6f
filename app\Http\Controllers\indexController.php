<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Mail;
use Illuminate\Http\Request;
use App\News;
use App\Tender;
use App\MailRecord;
use Illuminate\Support\Facades\DB;
use App\Metric;
use App\CustomClass\geoPlugin;

class indexController extends Controller
{
  public function index()
  {
    // $geoplugin = new geoPlugin();
    // // $geoplugin->locate();
    // $ua = strtolower($_SERVER['HTTP_USER_AGENT']);
    // $isMob = is_numeric(strpos($ua, "mobile"));
    // if ($isMob) {
    //   $device = 1;
    // } else {
    //   $device = 0;
    // }

    // Metric::create([
    //   "ip" => $geoplugin->ip,
    //   "city" => $geoplugin->city,
    //   "region" => $geoplugin->region,
    //   "regionCode" => $geoplugin->regionCode,
    //   "regionName" => $geoplugin->regionName,
    //   "dmaCode" => $geoplugin->dmaCode,
    //   "countryCode" => $geoplugin->countryCode,
    //   "countryName" => $geoplugin->countryName,
    //   "inEU" => $geoplugin->inEU,
    //   "continentCode" => $geoplugin->continentCode,
    //   "continentName" => $geoplugin->continentName,
    //   "latitude" => $geoplugin->latitude,
    //   "longitude" => $geoplugin->longitude,
    //   "timezone" => $geoplugin->timezone,
    //   "currencyCode" => $geoplugin->currencyCode,
    //   "currencySymbol" => $geoplugin->currencySymbol,
    //   "currencyConverter" => $geoplugin->currencyConverter,
    //   "device" => $device
    // ]);
    $getslider = News::where('Dbvanews', 0)->where('status', 0)->orderBy('NewsID', 'desc')->take(10)->get();
    $news = News::where('status', 0)->orderBy('NewsID', 'desc')->take(2)->get();
    $singlenews = DB::table('news')->where('status', 0)->skip(2)->take(1)->orderBy('NewsID', 'desc')->get();
    $newsbig = DB::table('news')->where('status', 0)->skip(3)->take(1)->orderBy('NewsID', 'desc')->get();
    $newssm = DB::table('news')->where('status', 0)->skip(4)->take(6)->orderBy('NewsID', 'desc')->get();
    $newsslider = DB::table('news')->where('status', 0)->skip(10)->take(6)->orderBy('NewsID', 'desc')->get();
    $newsbig2 = DB::table('news')->where('status', 0)->skip(16)->take(2)->orderBy('NewsID', 'desc')->get();
    $newssm2 = DB::table('news')->where('status', 0)->skip(18)->take(3)->orderBy('NewsID', 'desc')->get();
    $newssm3 = DB::table('news')->where('status', 0)->skip(21)->take(3)->orderBy('NewsID', 'desc')->get();

    return view('index')->with([
      'getslider' => $getslider,
      'news' => $news,
      'newsbig' => $newsbig,
      'newssm' => $newssm,
      'newsslider' => $newsslider,
      'newsbig2' => $newsbig2,
      'newssm2' => $newssm2,
      'newssm3' => $newssm3,
      'singlenews' => $singlenews
    ]);
  }

  public function media()
  {
    return view('media');
  }

  public function getnews($getnews)
  {
    $getnews = News::where('NewsID', $getnews)->where('status', 0)->first();
    if (!isset($getnews)) :
      abort(404);
    endif;
    DB::update('update news set Count_View = Count_View+3 where NewsID = ?', [$getnews->NewsID]);
    return view('news')->with(['getnews' => $getnews]);
  }

  public function allnews()
  {
    $allnews = News::where('status', 0)->orderBy('NewsID', 'DESC')->paginate(12);
    return view('allnews')->with('allnews', $allnews);
  }

  public function gettender($gettender)
  {
    $gettender = Tender::find($gettender);
    if (!isset($gettender)) :
      abort(404);
    endif;
    DB::update('update tenders set Count_View = Count_View+1 where TendersID = ?', [$gettender->TendersID]);
    return view('gettenders')->with('gettender', $gettender);
  }

  public function action(Request $request)
  {
    if ($request->ajax()) {
      $output = '';
      $query = $request->get('query');
      if ($query != '') {
        $data = MailRecord::where('directorate', 'like', '%' . $query . '%')
          ->orWhere('Category', 'like', '%' . $query . '%')
          ->orWhere('Email', 'like', '%' . $query . '%')
          ->orderBy('ID', 'desc')
          ->get();
      } else {
        $data = MailRecord::orderBy('ID', 'desc')->limit(100)->get();
      }
      $total_row = $data->count();
      if ($total_row > 0) {
        $output .= '<table style="background-color: #4d1015;" class="table table-bordered table-responsive table-sm table-hover table-dark "><tbody class="text-right">';
        if (!$query) {
          $output .= '<tr>
            <td>ديوان محافظة كركوك البريد العام</td>
            <td>بريد التخاطب الالكتروني</td>
            <td><EMAIL></td>
          </tr>';
        }
        foreach ($data as $row) {
          $output .= '
              <tr>
                <td>' . $row->directorate . '</td>
                <td>' . $row->Category . '</td>
                <td>' . $row->Email . '</td>
              </tr>';
        }
        $output .= '</tbody></table>';
      } else {
        $output = '<div style="text-align: center;">لايوجد نتائج بحث</div>';
      }
      $data = array(
        'table_data'  => $output
      );

      return json_encode($data);
    }
    return view('mailrecord');
  }

  public function contact()
  {
    return view('contact');
  }

  public function sendmsg(Request $request)
  {
    $this->validate($request, [
      'name' => 'required|min:2|string',
      'email' => 'required|email',
      'phone' => 'required|digits:11',
      'message' => 'required|min:10',
      'g-recaptcha-response' => 'required|captcha',
    ], [
      'name.required' => 'الاسم الكامل مطلوب',
      'name.min' => 'الاسم الكامل يجب ان يكون اكثر من 2 حرف',
      'email.required' => 'البريد الألكتروني مطلوب',
      'phone.required' => 'رقم الهاتف مطلوب',
      'phone.digits' => 'رقم الهاتف يجب ان يكون 11 رقم',
      'message.required' => 'نص الرسالة مطلوب',
      'message.min' => 'نص الرسالة لايجب ان تكون اقل من 10 احرف',
      'g-recaptcha-response.required' => 'التحقق مطلوب.',
      'g-recaptcha-response.captcha' => 'خطأ في التحقق.',
    ]);

    $data = array(
      'name' => $request->name,
      'email' => $request->email,
      'phone' => $request->phone,
      'bodyMessage' => $request->message
    );
    Mail::send('email.message', $data, function ($message) use ($data) {
      $message->from($data['email']);
      $message->to('<EMAIL>');
      $message->subject($data['name']);
    });
    return back()->with('success', 'تم ارسال الرسالة بنجاح, سوف نرد عليكم بأسرع وقت ممكن');
  }
  public function complaintsPOS()
  {
    return view('form.complaints-POS.complaintsPOS');
  }
}
