@extends('layout.main')

@section('title', 'نافذة التقديم على هوية المختار | ديوان محافظة كركوك')

@section('content')
<div class="modal fade" id="CropModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="CropModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="CropModalLabel">اقتصاص الصورة</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="cropimg" style="direction: ltr;">
                    <div class="row">
                        <div class="col-md-8">
                            <div style="min-height: 497px;max-height: 497px">
                                <img src="" id="sample_image" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview"></div>
                            <div class="btn-group mt-5" style="display: inherit!important;">
                                <button type="button" class="btn btn-primary" title="Zoom In" id="Zoom_In">
                                    <span>
                                        <span class="fa fa-search-plus"></span>
                                    </span>
                                </button>
                                <button type="button" class="btn btn-primary" title="Zoom Out" id="Zoom_Out">
                                    <span>
                                        <span class="fa fa-search-minus"></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="crop">اقتصاص</button>
            </div>
        </div>
    </div>
</div>
<div class="container">
    @if (session()->has('successsendalmkhtar'))
    <div class="alert alert-success text-center ml-4 custom-margin">
        {!! session('successsendalmkhtar') !!}
    </div>
    @else
    <div class="ml-4 text-right font-kufi-regular custom-margin-f">
        <h3 class="text-center font-alhuraa mt-3">شؤون المواطنين شعبة المختارين - نافذة التقديم على هوية المختار</h3>
        <form action="{{ route('form.identity-almkhtar.store') }}" method="POST" enctype="multipart/form-data">
            @csrf
            <div class="form-group mb-4">
                <label for="Input1">الاسم الرباعي</label>
                <input type="text" class="form-control @error('fullname') is-invalid @enderror"
                    value="{{ old('fullname') }}" id="Input1" name="fullname">
                @error('fullname')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <div class="form-group mb-4">
                <label for="Input2">محل وتاريخ الولادة</label>
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control @error('goverment') is-invalid @enderror"
                            name="goverment" id="Input2">
                        @error('goverment')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                    <div class="col-md-8">
                        <input type="text" data-toggle="datepicker" readonly
                            class="form-control @error('dateofbirth') is-invalid @enderror"
                            value="{{ old('dateofbirth') }}" id="Input2" name="dateofbirth">
                        @error('dateofbirth')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>
            </div>
            <div class="form-group mb-4">
                <label for="Input3">فصيلة الدم</label>
                <input type="text" class="form-control @error('bloodtype') is-invalid @enderror"
                    value="{{ old('bloodtype') }}" id="Input3" name="bloodtype">
                @error('bloodtype')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <div class="form-group mb-4">
                <label for="Input4">رقم الهاتف</label>
                <input type="number" class="form-control @error('phonenumber') is-invalid @enderror"
                    value="{{ old('phonenumber') }}" id="Input4" name="phonenumber">
                @error('phonenumber')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <div class="form-group mb-4">
                <label for="Input5">القضاء</label>
                <select class="form-control @error('elimination') is-invalid @enderror" id="Input5" name="elimination">
                    <option value="">اختر</option>
                    <option value="قضاء كركوك">
                        قضاء كركوك
                    </option>
                    <option value="قضاء الحويجة">
                        قضاء الحويجة
                    </option>
                    <option value="قضاء داقوق">
                        قضاء داقوق
                    </option>
                    <option value="قضاء دبس">
                        قضاء دبس
                    </option>
                </select>
                @error('elimination')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <div class="form-group mb-4">
                <label for="Input55">الناحية</label>
                <select class="form-control @error('side') is-invalid @enderror" id="Input55" name="side">
                    <option value="">اختر</option>
                </select>
                @error('side')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <div class="row g-3 ">
                <div class="col-md-4">
                    <label for="Input555">القرية او المحلة</label>
                    <select name="prefixvillage" class="form-control @error('prefixvillage') is-invalid @enderror"
                        id="Input5555">
                        <option value="">
                            اختر
                        </option>
                        <option value="قرية">
                            قرية
                        </option>
                        <option value="حي">
                            حي
                        </option>
                        <option value="محلة">
                            محلة
                        </option>
                    </select>
                    @error('prefixvillage')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
                <div class="col-md-8">
                    <label for="Input555" style="color: red">اكتب الاسم بدون كلمة حي او محلة او قرية</label>
                    <input type="text" class="form-control @error('village') is-invalid @enderror"
                        value="{{ old('village') }}" id="Input555" name="village">
                    @error('village')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="form-group mb-4 mt-3">
                <label for="Input6">رمز ورقم الهوية</label>
                <input type="text" class="form-control @error('codenumberidentity') is-invalid @enderror"
                    value="{{ old('codenumberidentity') }}" id="Input6" name="codenumberidentity">
                @error('codenumberidentity')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <div class="form-group mb-4">
                <label for="Input7">رمز ورقم الختم</label>
                <input type="text" class="form-control @error('numberofstamp') is-invalid @enderror"
                    value="{{ old('numberofstamp') }}" id="Input7" name="numberofstamp">
                @error('numberofstamp')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <!-- <div class="file-upload "> -->
            <div class="col-sm-12 d-flex flex-column align-items-center mb-2" style="">
                <label for="upload_image" class="btn btn-outline-secondary mb-2">الصورة الشخصية</label>
                <input type="file" class="d-none" id="upload_image" data-type="upload1">
                <input type="hidden" id="upload1" name="photo">
                <img style="width: 150px!important;" id="uploaded_image" src="">
            </div>
            <!-- </div> -->
            <hr>
            <div style="width: 300px; margin: 0 auto!important">
                <div class="form-group mb-4">
                    {!! NoCaptcha::renderJs() !!}
                    {!! NoCaptcha::display() !!}
                    <span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
                </div>
            </div>
            <div class="text-center">
                <button type="submit" class="btn btn-primary">ارسال</button>
            </div>
        </form>
    </div>
    @endif
    @endsection