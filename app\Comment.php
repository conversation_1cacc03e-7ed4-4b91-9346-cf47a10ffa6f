<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Complaint;
use App\User;
class Comment extends Model
{
	protected $table = 'comments';

    protected $fillable = [
        'comment', 'complaint_id', 'user_id',
    ];
    
    public function complaint()
    {
        return $this->belongsTo(Complaint::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
