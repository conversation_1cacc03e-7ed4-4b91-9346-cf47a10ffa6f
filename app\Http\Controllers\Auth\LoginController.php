<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\User;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/complaints';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }
    /**
     * Get the login username to be used by the controller.
     *
     * @return string
     */
    public function validateLogin(Request $request)
    {
        $this->validate($request, [
            'password' => ['required'],
            'username' => ['required', 'max:100', 'min:3', 'exists:usercomplaints'],
        ], [
            'password.required' => 'كلمة السر مطلوبة',
            'username.required' => 'اسم المستخدم مطلوب.',
            'username.max' => 'اسم المستخدم لايمكن ان يكون اكثر من 100 حرف.',
            'username.min' => 'اسم المستخدم لايمكن ان يكون اقل من 3 احرف.',
            'username.exists' => 'اسم المستخدم غير مسجل سابقاً.',
        ]);
    }
    public function username()
    {
        return 'username';
    }
}
