<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Jobs;

class JobsController extends Controller
{
    public function index()
    {
        return view('form.jobs.index');
    }
    public function store(Request $request)
    {
        $this->validate($request, [
            'f1'                      => 'required|mimes:png,jpg,jpeg|max:512',
            'f2'                      => 'required|unique:jobs,f2',
            'f3'                      => 'required',
            'f4'                      => 'required',
            'f5'                      => 'required',
            'f6'                      => 'required',
            'f7'                      => 'required|digits:11|unique:jobs,f7',
            'f9'                      => 'required',
            'f11'                     => 'mimes:png,jpg,jpeg|max:512',
            'f12'                     => 'required',
            'f17'                     => 'required',
            'f19'                     => 'mimes:png,jpg,jpeg|max:512',
            'f20'                     => 'required',
            'f23'                     => 'mimes:png,jpg,jpeg|max:512',
            'f24'                     => 'required',
            'f26'                     => 'required',
            'f27'                     => 'required',
            'f28'                     => 'required',
            'f29'                     => 'required',
            'f30'                     => 'required',
            'f31'                     => 'required',
            'f35'                     => 'required|mimes:pdf|max:1024',
            'f41'                     => 'required',
            'f43'                     => 'required',
            'f44'                     => 'required',
            'f45'                     => 'required',
            'f46'                     => 'required',
            'f47'                     => 'required|mimes:png,jpg,jpeg|max:512',
            'ff47'                    => 'required',
            'ff48'                    => 'required',
            'f52'                     => 'mimes:png,jpg,jpeg|max:512',
            'f53'                     => 'required',
            'f59'                     => 'required|mimes:pdf|max:512',
            'f60'                     => 'required|mimes:pdf|max:512',
            'g-recaptcha-response' => 'required|captcha',
        ], [
            'f1.required' => 'الصورة مطلوب',
            'f1.mimes' => 'نوع الملف غير مدعوم الملفات المدعومة (png,jpg,jpeg)',
            'f1.max' => 'حجم الملف كبير الحد الاقصى للحجم (512KB)',
            'f2.required' => 'الحقل مطلوب',
            'f2.unique' => 'هذا الاسم مسجل مسبقاً!',
            'f3.required' => 'الحقل مطلوب',
            'f4.required' => 'الحقل مطلوب',
            'f5.required' => 'الحقل مطلوب',
            'f6.required' => 'الحقل مطلوب',
            'f7.required' => 'الحقل مطلوب',
            'f7.digits' => 'يرجى ادخال 11 رقم',
            'f7.unique' => 'هذا الرقم مسجل مسبقاً!',
            'f9.required' => 'الحقل مطلوب',
            'f12.required' => 'الحقل مطلوب',
            'f17.required' => 'الحقل مطلوب',
            'f20.required' => 'الحقل مطلوب',
            'f24.required' => 'الحقل مطلوب',
            'f26.required' => 'الحقل مطلوب',
            'f27.required' => 'الحقل مطلوب',
            'f28.required' => 'الحقل مطلوب',
            'f29.required' => 'الحقل مطلوب',
            'f30.required' => 'الحقل مطلوب',
            'f31.required' => 'الحقل مطلوب',
            'f35.required' => 'الحقل مطلوب',
            'f41.required' => 'الحقل مطلوب',
            'f43.required' => 'الحقل مطلوب',
            'f44.required' => 'الحقل مطلوب',
            'f45.required' => 'الحقل مطلوب',
            'f46.required' => 'الحقل مطلوب',
            'f47.required' => 'الحقل مطلوب',
            'ff47.required' => 'الحقل مطلوب',
            'ff48.required' => 'الحقل مطلوب',
            'f47.mimes' => 'نوع الملف غير مدعوم الملفات المدعومة (png,jpg,jpeg)',
            'f47.max' => 'حجم الملف كبير الحد الاقصى للحجم (512KB)',
            'f52.mimes' => 'نوع الملف غير مدعوم الملفات المدعومة (png,jpg,jpeg)',
            'f52.max' => 'حجم الملف كبير الحد الاقصى للحجم (512KB)',
            'f53.required' => 'الحقل مطلوب',
            'f59.required' => 'الحقل مطلوب',
            'f59.mimes' => 'نوع الملف غير مدعوم الملفات المدعومة (pdf)',
            'f59.max' => 'حجم الملف كبير الحد الاقصى للحجم (512KB)',
            'f60.required' => 'الحقل مطلوب',
            'f60.mimes' => 'نوع الملف غير مدعوم الملفات المدعومة (pdf)',
            'f60.max' => 'حجم الملف كبير الحد الاقصى للحجم (512KB)',
            'g-recaptcha-response.required' => 'التحقق مطلوب.',
            'g-recaptcha-response.captcha' => 'خطأ في التحقق.',
        ]);
        $points = 0;
        if ($request->f43 == 'امتياز') {
            $points += 25;
        } elseif ($request->f43 == 'جيد جدا') {
            $points += 20;
        } elseif ($request->f43 == 'جيد') {
            $points += 15;
        } elseif ($request->f43 == 'متوسط') {
            $points += 10;
        } elseif ($request->f43 == 'مقبول') {
            $points += 5;
        }

        if ($request->f44 == '2021-2022') {
            $points += 1;
        } elseif ($request->f44 == '2020-2021') {
            $points += 2;
        } elseif ($request->f44 == '2019-2020') {
            $points += 3;
        } elseif ($request->f44 == '2018-2019') {
            $points += 4;
        } elseif ($request->f44 == '2017-2018') {
            $points += 5;
        } else {
            $points += 5;
        }

        if ($request->f12 != 'اعزب' && $request->f15 != 'موظف حكومي' && $request->f13 != '0') {
            $points += 10;
        } elseif ($request->f12 == 'متزوج' && $request->f15 != 'موظف حكومي' && $request->f13 == '0') {
            $points += 5;
        }

        $jobs = new Jobs();

        $jobs->f1        = $request->f1->store('/uploads/jobsfile', 'public');
        $jobs->f2        = $request->f2;
        $jobs->f3        = $request->f3;
        $jobs->f4        = $request->f4;
        $jobs->f5        = $request->f5;
        $jobs->f6        = $request->f6;
        $jobs->f7        = $request->f7;
        $jobs->f8        = $request->f8;
        $jobs->f9        = $request->f9;
        $jobs->f10        = $request->f10;
        $jobs->f11        = !empty($request->f11) ? $request->f11->store('/uploads/jobsfile', 'public') : '';
        $jobs->f12        = $request->f12;
        $jobs->f13        = $request->f13;
        $jobs->f14        = $request->f14;
        $jobs->f15        = $request->f15;
        $jobs->f16        = $request->f16;
        $jobs->f17        = $request->f17;
        $jobs->f18        = $request->f18;
        $jobs->f19        = !empty($request->f19) ? $request->f19->store('/uploads/jobsfile', 'public') : '';
        $jobs->f20        = $request->f20;
        $jobs->f21        = $request->f21;
        $jobs->f22        = $request->f22;
        $jobs->f23        = !empty($request->f23) ? $request->f23->store('/uploads/jobsfile', 'public') : '';
        $jobs->f24        = $request->f24;
        $jobs->f25        = 'كركوك';
        $jobs->f26        = $request->f26;
        $jobs->f27        = $request->f27;
        $jobs->f28        = $request->f28;
        $jobs->f29        = $request->f29;
        $jobs->f30        = $request->f30;
        $jobs->f31        = $request->f31;
        $jobs->f32        = $request->f32;
        $jobs->ff32        = $request->ff32;
        $jobs->f33        = $request->f33;
        $jobs->ff33        = $request->ff33;
        $jobs->f34        = $request->f34;
        $jobs->ff34        = $request->ff34;
        $jobs->f35        = $request->f35->store('/uploads/jobsfile', 'public');
        $jobs->f36        = $request->f36;
        $jobs->f37        = $request->f37;
        $jobs->f38        = $request->f38;
        $jobs->f39        = $request->f39;
        $jobs->f40        = $request->f40;
        $jobs->f41        = $request->f41;
        $jobs->f43        = $request->f43;
        $jobs->f44        = $request->f44;
        $jobs->f45        = $request->f45;
        $jobs->f46        = $request->f46;
        $jobs->f47        = !empty($request->f47) ? $request->f47->store('/uploads/jobsfile', 'public') : '';
        $jobs->ff47       = $request->ff47;
        $jobs->ff48       = $request->ff48;
        $jobs->f48        = $request->f48;
        $jobs->f49        = $request->f49;
        $jobs->f50        = $request->f50;
        $jobs->f51        = $request->f51;
        $jobs->f52        = !empty($request->f52) ? $request->f52->store('/uploads/jobsfile', 'public') : '';
        $jobs->f53        = $request->f53;
        $jobs->f54        = $request->f54;
        $jobs->f55        = $request->f55;
        $jobs->f56        = $request->f56;
        $jobs->f57        = $request->f57;
        $jobs->f58        = $request->f58;
        $jobs->f59        = $request->f59->store('/uploads/jobsfile', 'public');
        $jobs->f60        = $request->f60->store('/uploads/jobsfile', 'public');
        $jobs->points     = $points;
        $jobs->ip         = $request->ip();
        $jobs->save();
        return redirect()->back()->with([
            'success' => 'success',
            'formid' => $jobs->id,
            'fullname' => $request->f2,
            'department' => $request->ff47,
            'formdate' => date("Y/m/d", strtotime($jobs->created_at))
        ]);
    }
}
