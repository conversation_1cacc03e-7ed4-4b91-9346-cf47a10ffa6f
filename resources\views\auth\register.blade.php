@extends('layout.main')
@section('title', 'التسجيل في نظام الشكاوي')
@section('content')
<div class="container">
    <div class="justify-content-center font-kufi-regular">
        {{-- <h4 class="customH4">التسجيل في نظام الشكاوي</h4> --}}
        <div class="row">
            <div class="col-lg-4">
                <div class="text-center">
                    <img src="{{ url('content/uploads/complaints/comlogo.webp') }}" width="100%">
                </div>
            </div>
            <div class="col-lg-4">
                <div class="contact-form text-right" style="max-width: 100%; padding-top: 0!important">
                    <form method="POST" action="{{ route('register') }}">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="name" class="col-md-4 col-form-label text-md-right">اسمك الكامل</label>
                            <input id="name" type="text" class="form-control @error('name') is-invalid @enderror" name="name" value="{{ old('name') }}" autocomplete="name" autofocus>

                            @error('name')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="username" class="col-md-4 col-form-label text-md-right">اسم المستخدم</label>
                            <input id="username" type="text" class="form-control @error('username') is-invalid @enderror" name="username" value="{{ old('username') }}" autocomplete="off">

                            @error('username')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <div class="form-group mb-3">
                            <label for="password" class="col-md-4 col-form-label text-md-right">كلمة السر</label>
                            <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" value="{{ old('password') }}" autocomplete="new-password">

                            @error('password')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <div class="form-group mb-3">
                            <label for="password-confirm" class="col-md-4 col-form-label text-md-right">تأكيد كلمة السر</label>
                            <input id="password-confirm" type="password" class="form-control" name="password_confirmation" autocomplete="new-password">
                        </div>
                        <div class="mb-4" style="width: 300px; margin: 12px auto!important">
                            <div class="form-group">
                                {!! NoCaptcha::renderJs() !!}
                                {!! NoCaptcha::display() !!}
                                <span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
                            </div>
                        </div>
                        <div class="form-group mb-0 d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-block">
                                تسجيل
                            </button>
                        </div>
                    </form>
                    <hr>
                    <div class="text-center"><a style="font-size: 12px" href="{{ url('login') }}">اذا كنت مسجل لدينا اضغط هنا</a></div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection