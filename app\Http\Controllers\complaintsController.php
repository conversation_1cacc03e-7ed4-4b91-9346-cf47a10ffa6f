<?php

namespace App\Http\Controllers;

use App\Http\Requests\ComplaintRequest;
use App\Http\Requests\CommentRequest;
use Illuminate\Http\Request;
use App\Complaint;
use App\User;
use App\Comment;
use Illuminate\Support\Facades\Auth;

class complaintsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $userid = Auth::user()->id;
        $complaints = User::find($userid)->complaints;
        return view('complaints.index')->with('complaints', $complaints);
    }
    public function viewComplaints($complaints)
    {

        if (isset(Complaint::find($complaints)->user)) :
            $actioncomp = Complaint::find($complaints)->action;
            $comments   = Complaint::find($complaints)->comment;
            $complaints = Complaint::find($complaints);
        else :
            abort(404);
        endif;
        return view('complaints.complaint')
            ->with([
                'complaints' => $complaints,
                'actioncomp' => $actioncomp,
                'comments'   => $comments,
            ]);
    }

    public function sendComplaints()
    {
        return view('complaints.sendcomplaints');
    }

    /**
     * Store a new user.
     *
     * @param  Request  $request
     * @return Response
     */
    public function store(ComplaintRequest $request)
    {
        if ($request->file1 == null) {
            $file1 = "";
        } else {
            $file1 = $request->file1->store('/uploads/complaints', 'public');
        }
        if ($request->file2 == null) {
            $file2 = "";
        } else {
            $file2 = $request->file2->store('/uploads/complaints', 'public');
        }
        Complaint::create([
            'subject'               => $request->subject,
            'phone'               => $request->phone,
            'company'               => $request->company,
            'content'               => $request->msg,
            'file1'                 => $file1,
            'file2'                 => $file2,
            'user_id'               => Auth::user()->id,
            'statuscomment'         => 0,
            'statuscom'             => 0,
            'readablecomplaints'    => 0,
        ]);
        session()->flash('success', 'تم الاضافه بنجاح');
        return redirect(route('complaints'));
    }

    public function storeComment(Request $request)
    {
        $this->validate($request, [
            'comment'     => 'required|max:255',
        ], [
            'comment.required' => 'يرجى كتابة تعليق.',
            'comment.max' => 'لايمكن ان يكون التعليق اكثر من 500 حرف..',
        ]);

        $comment = new Comment();
        $comment->comment       = $request->comment;
        $comment->complaint_id  = $request->comid;
        $comment->user_id       = Auth::user()->id;
        $comment->save();
        return redirect()->back()->with('successcomment', 'تم اضافة التعليق بنجاح');
    }
}
