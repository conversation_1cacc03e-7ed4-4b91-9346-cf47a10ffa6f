@import url(//fonts.googleapis.com/earlyaccess/notokufiarabic.css);
@font-face {
  font-family: 'Al Hurra Txt Bold';
  font-style: normal;
  font-weight: 400;
  src: url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.eot);
  src: url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.eot?#iefix) format('embedded-opentype'),
       url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.woff2) format('woff2'),
       url(../fonts/Al-Hurra/AlHurraTxtBold.woff) format('woff'),
       url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.ttf) format('truetype');
}
body {
  font-family: 'Noto Kufi Arabic';
}
.imageUploadBox
{
    width: -webkit-fill-available;
    text-align: center;
    position: relative;
    width: 200px;
    margin:50px 28px;
    object-fit: cover;
}
input[type="file"] {
  box-sizing: border-box;
  width: 100%;
  height: calc(3em + 4px);
  margin: 0 0 1em;
  padding: 1em;
  padding-bottom: 230px;
  border: 2px solid #ccc;
  border-radius: 1em;
  background: #fff;
  resize: none;
  outline: none;
  align-content: center;
  text-align: center;
  
}
input[type="file"][required]:focus {
  border-color: #00bafa;
}
input[type="file"][required]:focus + label[placeholder]:before {
  color: #00bafa;
}
input[type="file"][required]:focus + label[placeholder]:before,
input[type="file"][required]:valid + label[placeholder]:before {
  -webkit-transition-duration: 0.2s;
          transition-duration: 0.2s;
  -webkit-transform: translate(0, -1.5em) scale(0.9, 0.9);
          transform: translate(0, -1.5em) scale(0.9, 0.9);
}
input[type="file"][required]:invalid + label[placeholder][alt]:before {
  content: attr(alt);
}
input[type="file"][required] + label[placeholder] {
  display: block;
  pointer-events: none;
  line-height: 1.25em;
  margin-top: calc(-15em - 12px);
  margin-bottom: calc((3em - 1em) + 4px);
}
input[type="file"][required] + label[placeholder]:before {
  content: attr(placeholder);
  display: inline-block;
  margin: 0 calc(1em + 4px);
  padding: 0 10px;
  color: #898989;
  white-space: nowrap;
  -webkit-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#fff));
  background-image: linear-gradient(to bottom, #fff, #fff);
  background-size: 100% 5px;
  background-repeat: no-repeat;
  background-position: center;
}

/*----------------------------------------*/

.file-upload {
  background-color: #ffffff;
  width: 600px;
  margin: 0 auto;
  padding: 20px;
}


.file-upload-content {
  display: none;
  text-align: center;
}

.file-upload-input {

  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  outline: none;
  opacity: 0;
  cursor: pointer;
}

.image-upload-wrap {
  margin-top: 20px;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 250px;
  margin: 0 auto;
  border: 2px solid #ccc;
  border-radius: 1em;
  background: #fff;
  resize: none;
  outline: none;
  align-content: center;
  text-align: center;
}

.image-dropping,
.image-upload-wrap:hover {
  background-color: white;
}

.image-title-wrap {
  color: #222;
}

.drag-text h3 {
  font-weight: 100;
  text-transform: uppercase;
  color: #15824B;
  padding: 60px 0;
}

.file-upload-image {
  box-sizing: border-box;
  width: 100%;
  height: 250px;
  margin: 0 auto;
  border: 2px solid #ccc;
  border-radius: 1em;
  background: #fff;
  resize: none;
  outline: none;
  align-content: center;
  text-align: center;
  object-fit: cover;
}

.remove-image {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  color: #fff;
  background: #cd4535;
  border: 5px solid white;
  border-radius: 50%;
  transition: all .2s ease;
  outline: none;
  text-transform: uppercase;
  font-weight: 700;
  position: relative;
  bottom:30px;
  left:50%;
}

.remove-image:hover {
  background: #c13b2a;
  color: #ffffff;
  transition: all .2s ease;
  cursor: pointer;
}

.remove-image:active {
  border: 0;
  transition: all .2s ease;
}
.input-group-text {
    display: block!important;
}
.form-control
{
    text-align: center!important;
}
.input-group-text {
    color: #ffffff;
    background-color: #8b0000;
    border: 1px solid #8b0000;
}