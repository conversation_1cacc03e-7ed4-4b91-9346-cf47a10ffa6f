@extends('layout.main')

@section('title', $getnews->Title)
@section('titlee', $getnews->Title)
@section('url', route('singlenews', $getnews->NewsID))
@section('site_name', $getnews->Title)
@section('description', strip_tags($getnews->Content))
@section('image', url('content/uploads') . '/' . $getnews->Image)

@section('content')
<?php
$dateCustom = explode('-', $getnews->Date);
switch ($dateCustom[1]) {
	case '1':
		$dateCustom2 = $dateCustom[2] . ' ' . 'كانون الثاني' . ' ' . $dateCustom[0];
		break;
	case '2':
		$dateCustom2 = $dateCustom[2] . ' ' . 'شباط' . ' ' . $dateCustom[0];
		break;
	case '3':
		$dateCustom2 = $dateCustom[2] . ' ' . 'آذار' . ' ' . $dateCustom[0];
		break;
	case '4':
		$dateCustom2 = $dateCustom[2] . ' ' . 'نيسان' . ' ' . $dateCustom[0];
		break;
	case '5':
		$dateCustom2 = $dateCustom[2] . ' ' . 'أيار' . ' ' . $dateCustom[0];
		break;
	case '6':
		$dateCustom2 = $dateCustom[2] . ' ' . 'حزيران' . ' ' . $dateCustom[0];
		break;
	case '7':
		$dateCustom2 = $dateCustom[2] . ' ' . 'تموز' . ' ' . $dateCustom[0];
		break;
	case '8':
		$dateCustom2 = $dateCustom[2] . ' ' . 'آب' . ' ' . $dateCustom[0];
		break;
	case '9':
		$dateCustom2 = $dateCustom[2] . ' ' . 'أيلول' . ' ' . $dateCustom[0];
		break;
	case '10':
		$dateCustom2 = $dateCustom[2] . ' ' . 'تشرين الأول' . ' ' . $dateCustom[0];
		break;
	case '11':
		$dateCustom2 = $dateCustom[2] . ' ' . 'تشرين الثاني' . ' ' . $dateCustom[0];
		break;
	case '12':
		$dateCustom2 = $dateCustom[2] . ' ' . 'كانون الأول' . ' ' . $dateCustom[0];
		break;
}
?>
<style>
	.x11i5rnm {
		font-family: 'Noto Kufi Arabic Regular' !important
	}
</style>
<div class="container">
	<div class="font-kufi-regular">
		<h3 class="text-center font-kufi-regular">{{ $getnews->Title }}</h3>
		<p class="text-center">
			<small class="text-muted text-center">
				نشر في {{ $dateCustom2 }}
			</small>
		</p>
		@if ($getnews->Dbvanews == 0)
		<div class="text-center mb-3">
			<img class="img-fluid" src="{{ url('content/uploads') . '/' . $getnews->Image }}"
				alt="{{ $getnews->Title }}" width="500">
		</div>
		@endif
		<div style="text-align: justify!important;line-height: 36px;">
			<p style="text-align: justify!important;font-family: 'Noto Kufi Arabic Regular'!important;">{!!
				$getnews->Content !!}</p>
		</div>

		@if (!empty($getnews->Video))
		<hr>
		<?php
		$videolink = $getnews->Video;
		$ytarray = explode("/", $videolink);
		$ytendstring = end($ytarray);
		$ytendarray = explode("?v=", $ytendstring);
		$ytendstring = end($ytendarray);
		$ytendarray = explode("&", $ytendstring);
		$ytcode = $ytendarray[0];
		?>
		<div class="text-center embed-responsive embed-responsive-16by9"><iframe class="embed-responsive-item"
				src="https://www.youtube.com/embed/{{ $ytcode }}" frameborder="0"
				allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
				allowfullscreen></iframe></div>
		@endif
		@if (!empty($getnews->VideoFacebook))
		<hr>
		<script async defer src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.2"></script>
		<?php $VideoFacebook = explode(',', $getnews->VideoFacebook); ?>
		<!-- Your embedded video player code -->
		@foreach ($VideoFacebook as $VideoFa)
		@if (!empty($VideoFa))
		<div id="fb-root"></div>
		<div class="fb-video" data-href="{{ $VideoFa }}" data-width="auto" data-show-text="false"></div>
		<hr>
		@endif
		@endforeach
		@endif
		<div class="text-center">
			@foreach (explode(',', $getnews->MultiImg) as $getImage)
			@if (!empty($getImage))
			<a data-fancybox="gallery" href="{{ url('content/uploads') . '/' . $getImage }}"><img class="img-thumbnail"
					style="padding:15px; margin:5px" src="{{ url('content/uploads') . '/' . $getImage }}"
					width="100px"></a>
			@endif
			@endforeach
		</div>
		<div class="text-right">
			@if (!empty($getnews->MultiFile))
			<hr>
			<div class="custom-atachment">
				<p class="fst-italic" style="font-size:12px;margin-bottom: 0;">المرفقات:</p>
				@foreach (explode(',', $getnews->MultiFile) as $getFile)
				<a href="{{ url('content/uploads') . '/' . $getFile }}"><i class="fas fa-paperclip"></i> {{
					str_replace(array("_", "-", "&", ".", "pdf", "xls", "doc"), " ", $getFile) }}</a><br />
				@endforeach
			</div>
			@endif
		</div>
		<hr>
		<div class="share-post">
			<h6 class="text-right">شارك هذا الموضوع ايضاً:</h6><br>
			<div class="share-post2">
				<div class="a2a_kit a2a_kit_size_32 a2a_default_style"
					data-a2a-url="{{ url('news') .'/'. $getnews->NewsID }}" data-a2a-title="{{ $getnews->Title }}">
					<a class="a2a_button_facebook"></a>
					<a class="a2a_button_twitter"></a>
					<a class="a2a_button_email"></a>
					<a class="a2a_button_yahoo_mail"></a>
					<a class="a2a_button_print"></a>
					<a class="a2a_button_google_gmail"></a>
					<a class="a2a_button_linkedin"></a>
				</div>
			</div>
		</div>
	</div>
</div>
@endsection