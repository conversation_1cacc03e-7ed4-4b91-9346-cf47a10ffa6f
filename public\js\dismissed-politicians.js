function readURL(a) { var e; a.files && a.files[0] ? ((e = new FileReader).onload = function (e) { $(".image-upload-wrap").hide(), $(".file-upload-image").attr("src", e.target.result), $(".file-upload-content").show(), $(".image-title").html(a.files[0].name) }, e.readAsDataURL(a.files[0])) : removeUpload() } function removeUpload() { $(".file-upload-input").replaceWith($(".file-upload-input").clone()), $(".file-upload-content").hide(), $(".image-upload-wrap").show() } $(".image-upload-wrap").bind("dragover", function () { $(".image-upload-wrap").addClass("image-dropping") }), $(".image-upload-wrap").bind("dragleave", function () { $(".image-upload-wrap").removeClass("image-dropping") }), $(function () { setInterval(function () { $(".alert-success").hide() }, 5e3) });