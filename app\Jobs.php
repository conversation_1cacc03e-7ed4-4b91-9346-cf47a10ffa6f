<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Jobs extends Model
{
    protected $table = 'jobs';
    protected $primaryKey = 'id';
    protected $fillable = [
        'f1',
        'f2',
        'f3',
        'f4',
        'f5',
        'f6',
        'f7',
        'f8',
        'f9',
        'f10',
        'f11',
        'f12',
        'f13',
        'f14',
        'f15',
        'f16',
        'f17',
        'f18',
        'f19',
        'f20',
        'f21',
        'f22',
        'f23',
        'f24',
        'f25',
        'f26',
        'f27',
        'f28',
        'f29',
        'f30',
        'f31',
        'f32',
        'ff32',
        'f33',
        'ff33',
        'f34',
        'ff34',
        'f35',
        'f36',
        'f37',
        'f38',
        'f39',
        'f40',
        'f41',
        'f43',
        'f44',
        'f45',
        'f46',
        'f47',
        'ff47',
        'ff48',
        'f48',
        'f49',
        'f50',
        'f51',
        'f52',
        'f53',
        'f54',
        'f55',
        'f56',
        'f57',
        'f58',
        'f59',
        'f60',
        'points',
        'ip',
    ];
}
