<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Style CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" integrity="sha384-+qdLaIRZfNu4cVPK/PxJJEy0B0f3Ugv8i482AKY7gwXwhaCroABd086ybrVKTa0q" crossorigin="anonymous">
    <link rel="stylesheet" href="{{asset('public/css/dismissed-politicians.css')}}">
    <title>استمارة معلومات رقم 1</title>
  </head>
  <body>
    

<div class="container">
    
    @if (session()->has('successsendforminfo1'))
    <div class="alert alert-success text-center ml-4 custom-margin">
        {!! session('successsendforminfo1') !!}
    </div>
    @endif
    <form action="{{ route('form.dismissed-politicians.store') }}" method="post" enctype="multipart/form-data">
        @csrf
        <div class="row" style="align-items: center">
            <div class="col-sm-4">
                <img class="img-fluid" src="{{ url('public/img/logo.webp') }}" width="250">
            </div>
            <div class="col-sm-4">
                <h2>استمارة معلومات رقم 1</h2>
            </div>
            <div class="col-sm-4">
                <div class="imageUploadBox float-end">
                    <div class="image-upload-wrap">
                        <input class="file-upload-input  @error('photo') is-invalid @enderror" type='file' name="photo" onchange="readURL(this);" accept="image/*" />
                        @error('photo')
                            <p class="text-danger">{{ $message }}</p>
                        @enderror
                        <label alt="صورة" placeholder="صورة"></label>
                    </div>
                    <div class="file-upload-content">
                        <img class="file-upload-image" src="#" alt="your image" />
                        <div class="image-title-wrap">
                            <button type="button" onclick="removeUpload()" class="remove-image">x</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row input-group mb-3">
            <h5 class="mb-3"><u>المعلومات الشخصية</u></h5>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="firstname">الاسم الاول</span>
                <input type="text" class="form-control @error('firstname') is-invalid @enderror" value="{{ old('firstname') }}" name="firstname" autofocus aria-describedby="firstname">
                @error('firstname')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="secondname">الاسم الثاني</span>
                <input type="text" class="form-control @error('secondname') is-invalid @enderror" value="{{ old('secondname') }}" name="secondname" aria-describedby="secondname">
                @error('secondname')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="thirdname">الاسم الثالث</span>
                <input type="text" class="form-control @error('thirdname') is-invalid @enderror" value="{{ old('thirdname') }}" name="thirdname" aria-describedby="thirdname">
                @error('thirdname')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="fourthname">الاسم الرابع</span>
                <input type="text" class="form-control @error('fourthname') is-invalid @enderror" value="{{ old('fourthname') }}" name="fourthname" aria-describedby="fourthname">
                @error('fourthname')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-4 mb-3">
                <span class="input-group-text" id="familyname">اللقب</span>
                <input type="text" class="form-control @error('familyname') is-invalid @enderror" value="{{ old('familyname') }}" name="familyname" aria-describedby="familyname">
                @error('familyname')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
        </div>
        <div class="row input-group mb-3">
            <div class="col-lg-3 mb-3">
                <span class="input-group-text" id="birthday">تاريخ الميلاد</span>
                <input type="date" class="form-control @error('birthday') is-invalid @enderror" value="{{ old('birthday') }}" name="birthday" aria-describedby="birthday">
                @error('birthday')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-3 mb-3">
                <span class="input-group-text" id="mothername">اسم الام الثلاثي</span>
                <input type="text" class="form-control @error('mothername') is-invalid @enderror" value="{{ old('mothername') }}" name="mothername" aria-describedby="mothername">
                @error('mothername')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-3 mb-3">
                <span class="input-group-text" id="mobile1">رقم الهاتف الاول</span>
                <input type="text" class="form-control @error('mobile1') is-invalid @enderror" value="{{ old('mobile1') }}" name="mobile1" aria-describedby="mobile1">
                @error('mobile1')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-3 mb-3">
                <span class="input-group-text" id="mobile2">رقم الهاتف الثاني</span>
                <input type="text" class="form-control @error('mobile2') is-invalid @enderror" value="{{ old('mobile2') }}" name="mobile2" aria-describedby="mobile2">
                @error('mobile2')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
        </div>
        <div class="row input-group mb-3">
            <h5 class="mb-3"><u>معلومات الهوية</u></h5>
            <div class="col-lg-4 mb-3">
                <span class="input-group-text" id="idnumber">رقم هوية الاحوال او البطاقة الموحدة</span>
                <input type="text" class="form-control @error('idnumber') is-invalid @enderror" value="{{ old('idnumber') }}" name="idnumber" aria-describedby="idnumber">
                @error('idnumber')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-4 mb-3">
                <span class="input-group-text" id="idissuer">جهة الاصدار</span>
                <input type="text" class="form-control @error('idissuer') is-invalid @enderror" value="{{ old('idissuer') }}" name="idissuer" aria-describedby="idissuer">
                @error('idissuer')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-4 mb-3">
                <span class="input-group-text" id="iddate">تاريخ الاصدار</span>
                <input type="date" class="form-control @error('iddate') is-invalid @enderror" value="{{ old('iddate') }}" name="iddate" aria-describedby="iddate">
                @error('iddate')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
        </div>
        <div class="row input-group mb-3">
            <h5 class="mb-3"><u>عنوان السكن السابق</u></h5>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="previousgov">المحافظة</span>
                <input type="text" class="form-control @error('previousgov') is-invalid @enderror" value="{{ old('previousgov') }}" name="previousgov" aria-describedby="previousgov">
                @error('previousgov')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="previouseliminate">القضاء</span>
                <input type="text" class="form-control @error('previouseliminate') is-invalid @enderror" value="{{ old('previouseliminate') }}" name="previouseliminate" aria-describedby="previouseliminate">
                @error('previouseliminate')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="previousside">الناحية</span>
                <input type="text" class="form-control @error('previousside') is-invalid @enderror" value="{{ old('previousside') }}" name="previousside" aria-describedby="previousside">
                @error('previousside')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="previoussection">الحي</span>
                <input type="text" class="form-control @error('previoussection') is-invalid @enderror" value="{{ old('previoussection') }}" name="previoussection" aria-describedby="previoussection">
                @error('previoussection')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="previousnumhome">رقم الدار</span>
                <input type="text" class="form-control @error('previousnumhome') is-invalid @enderror" value="{{ old('previousnumhome') }}" name="previousnumhome" aria-describedby="previousnumhome">
                @error('previousnumhome')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="previousclosestpoint">اقرب نقطة دالة</span>
                <input type="text" class="form-control @error('previousclosestpoint') is-invalid @enderror" value="{{ old('previousclosestpoint') }}" name="previousclosestpoint" aria-describedby="previousclosestpoint">
                @error('previousclosestpoint')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
        </div>
        <div class="row input-group mb-3">
            <h5 class="mb-3"><u>عنوان السكن الحالي</u></h5>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="currentgov">المحافظة</span>
                <input type="text" class="form-control @error('currentgov') is-invalid @enderror" value="{{ old('currentgov') }}" name="currentgov" aria-describedby="currentgov">
                @error('currentgov')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="currenteliminate">القضاء</span>
                <input type="text" class="form-control @error('currenteliminate') is-invalid @enderror" value="{{ old('currenteliminate') }}" name="currenteliminate" aria-describedby="currenteliminate">
                @error('currenteliminate')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="currentside">الناحية</span>
                <input type="text" class="form-control @error('currentside') is-invalid @enderror" value="{{ old('currentside') }}" name="currentside" aria-describedby="currentside">
                @error('currentside')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="currentsection">الحي</span>
                <input type="text" class="form-control @error('currentsection') is-invalid @enderror" value="{{ old('currentsection') }}" name="currentsection" aria-describedby="currentsection">
                @error('currentsection')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="currentnumhome">رقم الدار</span>
                <input type="text" class="form-control @error('currentnumhome') is-invalid @enderror" value="{{ old('currentnumhome') }}" name="currentnumhome" aria-describedby="currentnumhome">
                @error('currentnumhome')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
            <div class="col-lg-2 mb-3">
                <span class="input-group-text" id="currentclosestpoint">اقرب نقطة دالة</span>
                <input type="text" class="form-control @error('currentclosestpoint') is-invalid @enderror" value="{{ old('currentclosestpoint') }}" name="currentclosestpoint" aria-describedby="currentclosestpoint">
                @error('currentclosestpoint')
                    <p class="text-danger">{{ $message }}</p>
                @enderror
            </div>
        </div>
        <hr>
        <div style="width: 300px; margin: 0 auto!important">
            <div class="form-group">
                {!! NoCaptcha::renderJs() !!}
                {!! NoCaptcha::display() !!}
            <span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
            </div>
        </div>
        <div class="text-center mt-4 mb-5">
            <button type="submit" class="btn btn-primary btnSend">ارسال الطلب</button>
        </div>
    </form>
</div>
    <!-- JavaScript Files -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.10.2/dist/umd/popper.min.js" integrity="sha384-7+zCNj/IqJ95wo16oMtfsKbZ9ccEh31eOz1HGyDuCQ6wgnyJNSYdrPa03rtR1zdB" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js" integrity="sha384-QJHtvGhmr9XOIpI6YVutG+2QOK9T+ZnN4kzFN1RtK3zEFEIsxhlmWl5/YESvpZ13" crossorigin="anonymous"></script>
    <script src="{{asset('public/js/dismissed-politicians.js')}}"></script>
</body>
</html>