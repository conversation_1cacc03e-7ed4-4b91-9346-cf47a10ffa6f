<!DOCTYPE html>
<html dir="rtl">

<head>
    <meta charset="utf-8">
    <meta name="twitter:widgets:autoload" content="off">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta property="og:url" content="{{ url('') }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>نافذة اصدار هويات - مجلس محافظة كركوك</title>
    <!-- Css Files -->
    <link rel="shortcut icon" href="{{ asset('public/img/favicon.ico') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/css/datepicker.min.css') }}">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"
        integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/css/all.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/css/cropper.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/lib/select2/css/select2.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/lib/select2/css/select2-bootstrap.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('public/css/form.css') }}">
</head>

<body>
    <button style='position: fixed; right: 0;top: 10px' class='btn btn-info'
        onclick="window.history.go(-1); return false;">رجوع</button>
    <div class="modal fade" id="CropModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="CropModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="CropModalLabel">اقتصاص الصورة</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="cropimg" style="direction: ltr;">
                        <div class="row">
                            <div class="col-md-8">
                                <div style="min-height: 497px;max-height: 497px">
                                    <img src="" id="sample_image" />
                                </div>

                            </div>
                            <div class="col-md-4 text-center">
                                <div class="preview"></div>
                                <div class="btn-group mt-5" style="display: inherit!important;">
                                    <button type="button" class="btn btn-primary" title="Zoom In" id="Zoom_In">
                                        <span>
                                            <span class="fa fa-search-plus"></span>
                                        </span>
                                    </button>
                                    <button type="button" class="btn btn-primary" title="Zoom Out" id="Zoom_Out">
                                        <span>
                                            <span class="fa fa-search-minus"></span>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="crop">اقتصاص</button>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="custom-form">
            <h4 class="text-center">نافذة اصدار هويات - مجلس محافظة كركوك</h4>
            <hr width="100%" style="margin: auto; padding-bottom: 20px; padding-top: 10px">
            @if (session()->has('successsendidenf'))
            <div class="alert alert-success text-center">
                {!! session('successsendidenf') !!}
            </div>
            @else
            <form action="{{ route('kirkukprovincialcouncil.update', $identity->id) }}" style="position: relative;"
                method="POST" enctype="multipart/form-data">
                @csrf
                <div class="form-group row text-right">
                    <label class="col-sm-3 col-form-label">الحالة</label>
                    <div class="col-sm-4">
                        <input type="text" value="هوية مجلس" name="f1"
                            class="form-control @error('f1') is-invalid @enderror" readonly>
                        @error('f1')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                        @enderror
                    </div>
                </div>
                <div class="text-right" id="ifYes">
                    <div class="col-sm-3 d-flex flex-column align-items-center mb-2"
                        style="position: absolute; left: 0;top: -20px;">
                        <label for="upload_image" class="btn btn-outline-secondary mb-2">الصورة الشخصية</label>
                        <input type="file" class="d-none" id="upload_image" data-type="upload1">
                        <input type="hidden" id="upload1" name="f2">
                        <input type="hidden" name="oldf2" value="{{ $identity->f2 }}">
                        <img style="width: 150px!important;" id="uploaded_image"
                            src="{{asset('public/storage/' .$identity->f2)}}">
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">الاسم الثلاثي</label>
                        <div class="col-sm-4">
                            <input type="text" value="{{ $identity->f3 }}" readonly class="form-control">
                        </div>
                    </div>
                    <div class="form-group row" id="hiddennameen">
                        <label class="col-sm-3 col-form-label">الاسم باللغة الانكليزية</label>
                        <div class="col-sm-4">
                            <input type="text" id="requireCustom1" value="{{ $identity->f4 }}" name="f4"
                                pattern="\S(.*\S)?" class="form-control @error('f4') is-invalid @enderror"
                                onkeyup="this.value = this.value.toUpperCase();">
                            @error('f4')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row" id="hiddenjobs1">
                        <label class="col-sm-3 col-form-label">العنوان الوظيفي</label>
                        <div class="col-sm-4">
                            <select class="display-results @error('ff7') is-invalid @enderror"
                                data-searchable='searchable' name="ff7" required style="width: 100%;">
                                <option value="">اختر...</option>
                                @foreach (\Illuminate\Support\Facades\DB::table('jobs_and_section_title')
                                ->where('type', 1)->get() as $item)
                                <option value="{{$item->title}}" {{ $identity->ff7 == $item->title ? 'selected' : ''
                                    }}>{{$item->title}}</option>
                                @endforeach
                            </select>
                            @error('ff7')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row" id="hiddenjobnumber">
                        <label class="col-sm-3 col-form-label">الرقم الوظيفي</label>
                        <div class="col-sm-4">
                            <input type="text" id="requirejobnumber" value="{{ $identity->job_number }}"
                                name="job_number" class="form-control @error('job_number') is-invalid @enderror">
                            @error('job_number')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group row" id="hiddenallcat">
                        <label class="col-sm-3 col-form-label" id="hiddencat">القسم او الشعبة</label>
                        <div class="col-sm-4">
                            <select class="display-results @error('f6') is-invalid @enderror"
                                data-searchable='searchable' name="f6" required style="width: 100%;">
                                <option value="">اختر...</option>
                                @foreach (\Illuminate\Support\Facades\DB::table('jobs_and_section_title')
                                ->where('type', 2)->get() as $item)
                                <option value="{{$item->title}}" {{ $identity->f6 == $item->title ? 'selected' : ''
                                    }}>{{$item->title}}</option>
                                @endforeach
                            </select>
                            @error('f6')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">رقم الهاتف</label>
                            <div class="col-sm-4">
                                <input type="number" name="f10" value="{{ $identity->f10 }}"
                                    class="form-control @error('f10') is-invalid @enderror">
                                @error('f10')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">الحالة الزوجية</label>
                            <div class="col-sm-4">
                                <select name="f1new" class="form-control @error('f1new') is-invalid @enderror" required>
                                    <option value="">...</option>
                                    <option value="متزوج" {{ $identity->f1new == 'متزوج' ? 'selected' : '' }}>متزوج
                                    </option>
                                    <option value="اعزب" {{ $identity->f1new == 'اعزب' ? 'selected' : '' }}>اعزب
                                    </option>
                                    <option value="ارمل" {{ $identity->f1new == 'ارمل' ? 'selected' : '' }}>ارمل
                                    </option>
                                    <option value="مطلق" {{ $identity->f1new == 'مطلق' ? 'selected' : '' }}>مطلق
                                    </option>
                                </select>
                                @error('f1new')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">سنة التولد</label>
                            <div class="col-sm-4">
                                <select name="f2new" class="form-control @error('f2new') is-invalid @enderror" required>
                                    <option value="">...</option>
                                    @for ($i = date("Y") - 100; $i <= date("Y") - 18; $i++) <option value="{{$i}}" {{
                                        $identity->f2new == $i ? 'selected' : '' }}>
                                        {{$i}}
                                        </option>
                                        @endfor
                                </select>
                                @error('f2new')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group mb-3 row">
                            <label class="col-sm-3 col-form-label">رقم البطاقه الوطنية</label>
                            <div class="col-sm-3">
                                <input type="text" class="form-control @error('f3new') is-invalid @enderror"
                                    value="{{ $identity->f3new }}" name="f3new" required>
                                @error('f3new')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                            <div class="col-sm-2">
                                <select class="form-control @error('f4new') is-invalid @enderror" name="f4new" required>
                                    <option value="">محل اصدارها</option>
                                    <option value="كركوك" {{ $identity->f4new == 'كركوك' ? 'selected' : '' }}>كركوك
                                    </option>
                                    <option value="بغداد" {{ $identity->f4new == 'بغداد' ? 'selected' : '' }}>بغداد
                                    </option>
                                    <option value="البصرة" {{ $identity->f4new == 'البصرة' ? 'selected' : '' }}>البصرة
                                    </option>
                                    <option value="ميسان" {{ $identity->f4new == 'ميسان' ? 'selected' : '' }}>ميسان
                                    </option>
                                    <option value="ذي قار" {{ $identity->f4new == 'ذي قار' ? 'selected' : '' }}>ذي قار
                                    </option>
                                    <option value="الديوانية" {{ $identity->f4new == 'الديوانية' ? 'selected' : ''
                                        }}>الديوانية</option>
                                    <option value="المثنى" {{ $identity->f4new == 'المثنى' ? 'selected' : '' }}>المثنى
                                    </option>
                                    <option value="النجف الاشرف" {{ $identity->f4new == 'النجف الاشرف' ? 'selected' : ''
                                        }}>النجف الاشرف</option>
                                    <option value="كربلاء المقدسة" {{ $identity->f4new == 'كربلاء المقدسة' ? 'selected'
                                        : '' }}>كربلاء المقدسة</option>
                                    <option value="بابل" {{ $identity->f4new == 'بابل' ? 'selected' : '' }}>بابل
                                    </option>
                                    <option value="واسط" {{ $identity->f4new == 'واسط' ? 'selected' : '' }}>واسط
                                    </option>
                                    <option value="ديالى" {{ $identity->f4new == 'ديالى' ? 'selected' : '' }}>ديالى
                                    </option>
                                    <option value="صلاح الدين" {{ $identity->f4new == 'صلاح الدين' ? 'selected' : ''
                                        }}>صلاح الدين</option>
                                    <option value="نينوى" {{ $identity->f4new == 'نينوى' ? 'selected' : '' }}>نينوى
                                    </option>
                                    <option value="الانبار" {{ $identity->f4new == 'الانبار' ? 'selected' : ''
                                        }}>الانبار</option>
                                    <option value="اربيل" {{ $identity->f4new == 'اربيل' ? 'selected' : '' }}>اربيل
                                    </option>
                                    <option value="دهوك" {{ $identity->f4new == 'دهوك' ? 'selected' : '' }}>دهوك
                                    </option>
                                    <option value="سليمانية" {{ $identity->f4new == 'سليمانية' ? 'selected' : ''
                                        }}>سليمانية</option>
                                </select>
                                @error('f4new')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                            <label class="col-sm-2 col-form-label">تاريخ اصدارها</label>
                            <div class="col-sm-2">
                                <input type="date" class="form-control @error('f5new') is-invalid @enderror"
                                    value="{{ $identity->f5new }}" name="f5new" required>
                                @error('f5new')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">رقم الجواز</label>
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="f6new" value="{{ $identity->f6new }}">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">عنوان السكن</label>
                            <div class="col-sm-3">
                                <input type="text" name="f13" value="{{ $identity->f13 }}"
                                    class="form-control @error('f13') is-invalid @enderror" placeholder="المحافظة">
                                @error('f13')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                            <div class="col-sm-3">
                                <input type="text" name="f14" value="{{ $identity->f14 }}"
                                    class="form-control @error('f14') is-invalid @enderror" placeholder="القضاء">
                                @error('f14')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                            <div class="col-sm-3">
                                <input type="text" name="f15" value="{{ $identity->f15 }}"
                                    class="form-control @error('f15') is-invalid @enderror" placeholder="الناحيه">
                                @error('f15')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-sm-3">
                                <input type="text" name="f16" value="{{ $identity->f16 }}" class="form-control"
                                    placeholder="محلة">
                            </div>
                            <div class="col-sm-3">
                                <input type="text" name="f17" value="{{ $identity->f17 }}" class="form-control"
                                    placeholder="زقاق">
                            </div>
                            <div class="col-sm-3">
                                <input type="text" name="f18" value="{{ $identity->f18 }}" class="form-control"
                                    placeholder="دار">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">اقرب نقطة دالة</label>
                            <div class="col-sm-3">
                                <input type="text" name="f19" value="{{ $identity->f19 }}"
                                    class="form-control @error('f19') is-invalid @enderror">
                                @error('f19')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label">هل تملك مركبة خاصة او حكومية</label>
                            <div class="col-sm-6" id="hiddenNote" {{ $identity->f20 == 'نعم' ? 'style=display:none;' :
                                '' }}>
                                <small class="text-danger">ملاحظة: هذا الحقل يملأ فقط في حالة كنت من احد الاقسام
                                    المشموله بدخول مركبتكم في مراَب ديوان المحافظة او كنت تملك تصريح دخول</small>
                            </div>
                            <div class="col-sm-3">
                                <select class="form-control" id="requireCustom55" name="f20"
                                    onchange="yesnoCheck2(this);">
                                    <option value="">اختر</option>
                                    <option value="نعم" {{ $identity->f20 == 'نعم' ? 'selected' : '' }}>نعم</option>
                                    <option value="لا" {{ $identity->f20 == 'لا' ? 'selected' : '' }}>لا</option>
                                </select>
                            </div>
                            <div class="col-sm-6" id="customfile" {{ $identity->f20 == 'لا' ? 'style=display:none;' : ''
                                }}>
                                <div class="custom-file">
                                    <input type="hidden" value="{{ $identity->f21 }}" name="oldf21">
                                    <label class="custom-file-label text-center" for="inputGroupFile01">رفع نسخة من
                                        سنوية المركبة</label>
                                    <input type="file" accept="image/*" name="f21"
                                        class="custom-file-input @error('f21') is-invalid @enderror"
                                        id="inputGroupFile01" id="haveCar" aria-describedby="inputGroupFileAddon01">
                                    @error('f21')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="form-group row" id="ifYes2" {{ $identity->f20 == 'لا' ? 'style=display:none;' : ''
                            }}>
                            <div class="col-sm-4">
                                <input type="text" name="f22" id="haveCar1" class="form-control"
                                    placeholder="اسم صاحب السنوية">
                            </div>
                            <div class="col-sm-2">
                                <input type="text" name="f23" id="haveCar2" class="form-control" placeholder="رقمها">
                            </div>
                            <div class="col-sm-2">
                                <select class="form-control" name="f24" id="haveCar3">
                                    <option value="">المحافظة</option>
                                    <option value="حكومي" {{ $identity->f24 == 'حكومي' ? 'selected' : '' }}>حكومي
                                    </option>
                                    <option value="منفيس" {{ $identity->f24 == 'منفيس' ? 'selected' : '' }}>منفيس
                                    </option>
                                    <option value="كركوك" {{ $identity->f24 == 'كركوك' ? 'selected' : '' }}>كركوك
                                    </option>
                                    <option value="بغداد" {{ $identity->f24 == 'بغداد' ? 'selected' : '' }}>بغداد
                                    </option>
                                    <option value="البصرة" {{ $identity->f24 == 'البصرة' ? 'selected' : '' }}>البصرة
                                    </option>
                                    <option value="ميسان" {{ $identity->f24 == 'ميسان' ? 'selected' : '' }}>ميسان
                                    </option>
                                    <option value="ذي قار" {{ $identity->f24 == 'ذي' ? 'selected' : '' }}>ذي قار
                                    </option>
                                    <option value="الديوانية" {{ $identity->f24 == 'الديوانية' ? 'selected' : ''
                                        }}>الديوانية</option>
                                    <option value="المثنى" {{ $identity->f24 == 'المثنى' ? 'selected' : '' }}>المثنى
                                    </option>
                                    <option value="النجف الاشرف" {{ $identity->f24 == 'النجف الاشرف' ? 'selected' : ''
                                        }}>النجف الاشرف</option>
                                    <option value="كربلاء المقدسة" {{ $identity->f24 == 'كربلاء المقدسة' ? 'selected' :
                                        '' }}>كربلاء المقدسة</option>
                                    <option value="بابل" {{ $identity->f24 == 'بابل' ? 'selected' : '' }}>بابل</option>
                                    <option value="واسط" {{ $identity->f24 == 'واسط' ? 'selected' : '' }}>واسط</option>
                                    <option value="ديالى" {{ $identity->f24 == 'ديالى' ? 'selected' : '' }}>ديالى
                                    </option>
                                    <option value="صلاح الدين" {{ $identity->f24 == 'صلاح' ? 'selected' : '' }}>صلاح
                                        الدين</option>
                                    <option value="نينوى" {{ $identity->f24 == 'نينوى' ? 'selected' : '' }}>نينوى
                                    </option>
                                    <option value="الانبار" {{ $identity->f24 == 'الانبار' ? 'selected' : '' }}>الانبار
                                    </option>
                                    <option value="اربيل" {{ $identity->f24 == 'اربيل' ? 'selected' : '' }}>اربيل
                                    </option>
                                    <option value="دهوك" {{ $identity->f24 == 'دهوك' ? 'selected' : '' }}>دهوك</option>
                                    <option value="سليمانية" {{ $identity->f24 == 'سليمانية' ? 'selected' : ''
                                        }}>سليمانية</option>
                                </select>
                            </div>
                            <div class="col-sm-2">
                                <input type="text" name="f25" id="haveCar4" class="form-control" placeholder="نوعها">
                            </div>
                            <div class="col-sm-2">
                                <input type="text" name="f26" id="haveCar5" class="form-control" placeholder="موديلها">
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <input type="submit" value="تحديث" class="btn btn-primary">
                        </div>
                    </div>
                    <div id="noyet" style="display: none;">
                        <h4 class="text-center text-danger">غير متاح حالياً</h4>
                    </div>
            </form>
            @endif
        </div>
    </div>
    <!-- JavaScript Files -->
    <script src="{{ asset('public/js/jquery-3.4.1.min.js') }}"></script>
    <script src="{{ asset('public/js/popper.min.js') }}"></script>
    <script src="{{ asset('public/js/datepicker.min.js') }}"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"
        integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous">
    </script>
    <script src="{{ asset('public/js/all.min.js') }}"></script>
    <script src="{{ asset('public/js/cropper.min.js') }}"></script>
    <script src="{{ asset('public/lib/select2/js/select2.full.min.js') }}"></script>
    <script src="{{ asset('public/js/form.js') }}"></script>
    <script>
        $(document).ready(function() {

        $("body [data-searchable='searchable']").select2({
        dir: 'rtl',
        theme: 'bootstrap',
        language: 'ar',
        placeholder: 'اختر...',
        });
        var $modal = $('#CropModal');

        var image = document.getElementById('sample_image');

        var cropper;

        $('#upload_image').change(function(event) {
            var files = event.target.files;
            var thisinput = $(this);
            var done = function(url) {
                image.src = url;
                $modal.modal('show');
                $("#crop").attr('data-upload-image', thisinput.attr('data-type'));
            };

            if (files && files.length > 0) {
                reader = new FileReader();
                reader.onload = function(event) {
                    done(reader.result);
                };
                reader.readAsDataURL(files[0]);
            }
        });

        $modal.on('shown.bs.modal', function() {
            cropper = new Cropper(image, {
                dragMode: 'move',
                aspectRatio: 2.5 / 3.0,
                autoCropArea: 0.65,
                restore: false,
                guides: false,
                center: false,
                highlight: false,
                cropBoxMovable: false,
                cropBoxResizable: false,
                toggleDragModeOnDblclick: false,
            });
        }).on('hidden.bs.modal', function() {
            cropper.destroy();
            cropper = null;
        });

        $('#crop').click(function() {
            var dataUploadImage = $(this).attr('data-upload-image');
            canvas = cropper.getCroppedCanvas({
                width: 400,
                height: 600
            });
            canvas.toBlob(function(blob) {
                url = URL.createObjectURL(blob);
                var reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = function() {
                    var base64data = reader.result;
                    $('#uploaded_image').attr('src', base64data);
                    $("#upload1").val(base64data);

                    $modal.modal('hide');
                };
            });
        });
        $('#Zoom_In').click(function() {
            cropper.zoom(0.1);
        });
        $('#Zoom_Out').click(function() {
            cropper.zoom(-0.1);
        });
        });
    </script>
</body>

</html>