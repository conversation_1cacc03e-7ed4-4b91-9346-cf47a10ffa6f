{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2", "alexpechkarev/google-maps": "7.1", "anhskohbo/no-captcha": "^3.1", "aws/aws-sdk-php": "~3.0", "cloudlink/textmagic-laravel": "^1.0", "cyrildewit/eloquent-viewable": "^4.1", "facade/ignition": "^1.14", "fideloper/proxy": "^4.0", "filp/whoops": "^2.7", "gr8shivam/laravel-sms-api": "^3.0", "guzzlehttp/guzzle": "^6.5", "hashids/hashids": "^4.0", "laravel/framework": "^6.2", "laravel/tinker": "^2.0", "laravel/ui": "^1.1", "merujan99/laravel-video-embed": "dev-master", "nesbot/carbon": "^2.29", "nexmo/client": "^2.4", "silber/page-cache": "^1.0", "spatie/laravel-sitemap": "^5.7", "spatie/laravel-url-signer": "^2.5", "te7a-houdini/laravel-trix": "^2.0", "textmagic/sdk": "dev-master", "tm4b/tm4b-php": "^1.0", "twilio/sdk": "^6.10", "vonage/client": "^2.4", "wildbit/swiftmailer-postmark": "^3.1"}, "require-dev": {"facade/ignition": "^1.4", "fzaninotto/faker": "^1.4", "laravel/ui": "^1.1", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^8.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform": {"php": "7.3"}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}