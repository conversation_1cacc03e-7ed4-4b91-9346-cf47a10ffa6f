@extends('layout.mainkirkukcouncil')

@section('title', 'نافذة اصدار هويات - مجلس محافظة كركوك')

@section('content')
@if (session('kirkukprovincialcouncilpassword') == true)
<div class="upbar text-center bg-dark p-2">
    <h4 class="text-white">نافذة اصدار الهويات</h4>
</div>
<div class="container">
    <div class="row align-items-center">

        <div class="col-sm-6">
            <div class="dailyui">
                <div class="CTA">
                    <h1 onclick="location.reload();">تحديث</h1>
                </div>
                <div class="leftbox">
                    <nav>
                        <a id="status1" class="active" title="قــــيـــد الــمـراجـــــعة"><i
                                style="color:rgb(53, 53, 53)" class="far fa-clock fa-2x"></i></a>
                        <a id="status2" title="الاستمارة قيد الانجاز"><i style="color:#ffc107"
                                class="fas fa-hourglass-end fa-2x"></i></a>
                        <a id="status3" title="تـــــم طبــــع الهـويــــة"><i style="color:#28a745"
                                class="fas fa-check-square fa-2x"></i></a>
                        <a id="status4" title="طلب تجديد الهوية"><i style="color:#17a2b8"
                                class="fas fa-sync-alt fa-2x"></i></a>
                        <a id="status5" title="نقص بيانات الاستمارة"><i style="color:#f00"
                                class="fas fa-syncsdsdsd-alt fa-2x"></i></a>
                        <a id="status6" title="تم رفض الاستمارة"><i style="color:#f00"
                                class="fas fa-window-close fa-2x"></i></a>
                    </nav>
                </div>
                <div class="rightbox">
                    <div class="status1">
                        <h1>قــــيـــد الــمـراجـــــعة</h1>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last24hours', 'where' => 'قيد_المراجعة']) }}"
                                class="text-decoration-none text-dark">عرض اخر 24 ساعه</a> ({{
                            \App\Identity2::where('status', 'قيد المراجعة')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today())->get()->count() }})</span></h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last7days', 'where' => 'قيد_المراجعة']) }}"
                                class="text-decoration-none text-dark">عرض اخر 7 ايـام</a> ({{
                            \App\Identity2::where('status', 'قيد المراجعة')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today()->subDays(7))->get()->count() }})</span></h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last30days', 'where' => 'قيد_المراجعة']) }}"
                                class="text-decoration-none text-dark">عرض اخر 30 يـوم</a> ({{
                            \App\Identity2::where('status', 'قيد المراجعة')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today()->subDays(30))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'getall', 'where' => 'قيد_المراجعة']) }}"
                                class="text-decoration-none text-dark">عـــرض الــــكل</a> ({{
                            \App\Identity2::where('status', 'قيد المراجعة')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::createFromFormat('Y-m-d', '2022-05-1')->toDateTimeString())->get()->count()
                            }})</h6>
                    </div>
                    <div class="status2 noshow">
                        <h1>الاستمارة قيد الانجاز</h1>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last24hours', 'where' => 'قيد_الانجاز']) }}"
                                class="text-decoration-none text-dark">عرض اخر 24 ساعه</a> ({{
                            \App\Identity2::where('status', 'قيد الانجاز')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today())->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last7days', 'where' => 'قيد_الانجاز']) }}"
                                class="text-decoration-none text-dark">عرض اخر 7 ايـام</a> ({{
                            \App\Identity2::where('status', 'قيد الانجاز')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today()->subDays(7))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last30days', 'where' => 'قيد_الانجاز']) }}"
                                class="text-decoration-none text-dark">عرض اخر 30 يـوم</a> ({{
                            \App\Identity2::where('status', 'قيد الانجاز')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today()->subDays(30))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'getall', 'where' => 'قيد_الانجاز']) }}"
                                class="text-decoration-none text-dark">عـــرض الــــكل</a> ({{
                            \App\Identity2::where('status', 'قيد الانجاز')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::createFromFormat('Y-m-d', '2022-05-1')->toDateTimeString())->get()->count()
                            }})</h6>
                    </div>
                    <div class="status3 noshow">
                        <h1>تـــــم طبــــع الهـويــــة</h1>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last24hours', 'where' => 'انجزت']) }}"
                                class="text-decoration-none text-dark">عرض اخر 24 ساعه</a> ({{
                            \App\Identity2::where('status', 'انجزت')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today())->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last7days', 'where' => 'انجزت']) }}"
                                class="text-decoration-none text-dark">عرض اخر 7 ايـام</a> ({{
                            \App\Identity2::where('status', 'انجزت')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today()->subDays(7))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last30days', 'where' => 'انجزت']) }}"
                                class="text-decoration-none text-dark">عرض اخر 30 يـوم</a> ({{
                            \App\Identity2::where('status', 'انجزت')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today()->subDays(30))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'getall', 'where' => 'انجزت']) }}"
                                class="text-decoration-none text-dark">عـــرض الــــكل</a> ({{
                            \App\Identity2::where('status', 'انجزت')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::createFromFormat('Y-m-d', '2022-05-1')->toDateTimeString())->get()->count()
                            }})</h6>
                    </div>
                    <div class="status4 noshow">
                        <h1>طلب تجديد الهوية</h1>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last24hours', 'where' => 'تجديد']) }}"
                                class="text-decoration-none text-dark">عرض اخر 24 ساعه</a> ({{
                            \App\Identity2::where('status', 'تجديد')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today())->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last7days', 'where' => 'تجديد']) }}"
                                class="text-decoration-none text-dark">عرض اخر 7 ايـام</a> ({{
                            \App\Identity2::where('status', 'تجديد')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today()->subDays(7))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last30days', 'where' => 'تجديد']) }}"
                                class="text-decoration-none text-dark">عرض اخر 30 يـوم</a> ({{
                            \App\Identity2::where('status', 'تجديد')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::today()->subDays(30))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'getall', 'where' => 'تجديد']) }}"
                                class="text-decoration-none text-dark">عـــرض الــــكل</a> ({{
                            \App\Identity2::where('status', 'تجديد')->where('delete_status',
                            0)->whereDate('date_status', '>=',
                            \Carbon\Carbon::createFromFormat('Y-m-d', '2022-05-1')->toDateTimeString())->get()->count()
                            }})</h6>
                    </div>
                    <div class="status5 noshow">
                        <h1>نقص بيانات الاستمارة</h1>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last24hours', 'where' => 'نقص']) }}"
                                class="text-decoration-none text-dark">عرض اخر 24 ساعه</a> ({{
                            \App\Identity2::where('status', 'نقص')->where('delete_status', 0)->whereDate('date_status',
                            '>=',
                            \Carbon\Carbon::today())->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last7days', 'where' => 'نقص']) }}"
                                class="text-decoration-none text-dark">عرض اخر 7 ايـام</a> ({{
                            \App\Identity2::where('status', 'نقص')->where('delete_status', 0)->whereDate('date_status',
                            '>=',
                            \Carbon\Carbon::today()->subDays(7))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last30days', 'where' => 'نقص']) }}"
                                class="text-decoration-none text-dark">عرض اخر 30 يـوم</a> ({{
                            \App\Identity2::where('status', 'نقص')->where('delete_status', 0)->whereDate('date_status',
                            '>=',
                            \Carbon\Carbon::today()->subDays(30))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'getall', 'where' => 'نقص']) }}"
                                class="text-decoration-none text-dark">عـــرض الــــكل</a> ({{
                            \App\Identity2::where('status', 'نقص')->where('delete_status', 0)->whereDate('date_status',
                            '>=',
                            \Carbon\Carbon::createFromFormat('Y-m-d', '2022-05-1')->toDateTimeString())->get()->count()
                            }})</h6>
                    </div>
                    <div class="status6 noshow">
                        <h1>تم رفض الاستمارة</h1>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last24hours', 'where' => 'رفض']) }}"
                                class="text-decoration-none text-dark">عرض اخر 24 ساعه</a> ({{
                            \App\Identity2::where('status', 'رفض')->where('delete_status', 0)->whereDate('date_status',
                            '>=',
                            \Carbon\Carbon::today())->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last7days', 'where' => 'رفض']) }}"
                                class="text-decoration-none text-dark">عرض اخر 7 ايـام</a> ({{
                            \App\Identity2::where('status', 'رفض')->where('delete_status', 0)->whereDate('date_status',
                            '>=',
                            \Carbon\Carbon::today()->subDays(7))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'last30days', 'where' => 'رفض']) }}"
                                class="text-decoration-none text-dark">عرض اخر 30 يـوم</a> ({{
                            \App\Identity2::where('status', 'رفض')->where('delete_status', 0)->whereDate('date_status',
                            '>=',
                            \Carbon\Carbon::today()->subDays(30))->get()->count() }})</h6>
                        <h6><a href="{{ route('kirkukprovincialcouncil.fetch', ['date' => 'getall', 'where' => 'رفض']) }}"
                                class="text-decoration-none text-dark">عـــرض الــــكل</a> ({{
                            \App\Identity2::where('status', 'رفض')->where('delete_status', 0)->whereDate('date_status',
                            '>=',
                            \Carbon\Carbon::createFromFormat('Y-m-d', '2022-05-1')->toDateTimeString())->get()->count()
                            }})</h6>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <header>
                <img class="img-fluid" src="{{ asset('public/kirkukprovincialcouncil/asset/img/logo.webp') }}"
                    alt="Logo">
            </header>
            <div class="main">
                <div class="mt-5 button-form">
                    <a href="{{ route('form.kirkukprovincialcouncil.identity') }}" target="_blank"
                        class="btn btn-primary">التقديم على استمارة الهويات</a>
                </div>
            </div>
            <div class="searchbox">
                <input type="hidden" value="{{route('kirkukprovincialcouncil.searchajax')}}" id="routename">
                <div class="input-group mt-3">
                    <button class="btn btn-outline-secondary" type="button" id="searchboxbtn"><i
                            class="fas fa-search"></i></button>
                    <input type="text" class="form-control" id="searchbox" placeholder="رقم الاستمارة والاسم"
                        aria-describedby="searchboxbtn">

                    <button class="btn btn-outline-secondary ms-2" type="button" id="searchboxbtnidnumber"><i
                            class="fas fa-search"></i></button>
                    <input type="text" class="form-control" id="searchboxidnumber" placeholder="بحث عن رقم الهوية"
                        aria-describedby="searchboxbtnidnumber">
                </div>
                <div class="result"></div>
            </div>
        </div>
    </div>
</div>
@else
<div class="login">
    <div class="form">
        <form class="login-form" action="{{ route('kirkuk.provincial.council.password') }}" method="POST">
            @csrf
            <input type="password" name="password"
                class="form-control @if (session('errorkirkukprovincialcouncilpassword')) is-invalid @endif"
                placeholder="كلمة السر" required />
            @if (session('errorkirkukprovincialcouncilpassword'))
            <div class="invalid-feedback">
                {{ session('errorkirkukprovincialcouncilpassword') }}
            </div>
            @endif
            <button>دخول</button>
        </form>
    </div>
</div>
@endif
@endsection