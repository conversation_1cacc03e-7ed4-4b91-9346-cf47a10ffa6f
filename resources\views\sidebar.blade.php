<!-- Sidebar Start -->

<div class="col-lg-4">
    <div class="position-sticky" style="top: 10rem;">
        <!-- Category Start -->
        <div class="mb-5" data-wow-delay="0.1s">
            <div class="section-title section-title-sm position-relative pb-2 mb-3">
                <h5 class="mb-0 font-alhuraa">الاقسام الاخبارية</h5>
            </div>
            <div class="link-animated d-flex flex-column justify-content-start">
                <a class="h6 fw-semi-bold bg-light rounded py-2 px-3 mb-2 font-alhuraa" href="#"><i
                        class="bi bi-arrow-right me-2"></i> اخبار المحافظة</a>
                <a class="h6 fw-semi-bold bg-light rounded py-2 px-3 mb-2 font-alhuraa" href="#"><i
                        class="bi bi-arrow-right me-2"></i> اخبار المشاريع</a>
                <a class="h6 fw-semi-bold bg-light rounded py-2 px-3 mb-2 font-alhuraa" href="#"><i
                        class="bi bi-arrow-right me-2"></i> اخبار الدوائر الخدمية</a>
                <a class="h6 fw-semi-bold bg-light rounded py-2 px-3 mb-2 font-alhuraa" href="#"><i
                        class="bi bi-arrow-right me-2"></i> الاعلانات والمناقصات</a>
                <a class="h6 fw-semi-bold bg-light rounded py-2 px-3 mb-2 font-alhuraa" href="#"><i
                        class="bi bi-arrow-right me-2"></i> اخبار المعاون المحافظ الاداري</a>
                <a class="h6 fw-semi-bold bg-light rounded py-2 px-3 mb-2 font-alhuraa" href="#"><i
                        class="bi bi-arrow-right me-2"></i> اخبار المعاون المحافظ الفني</a>
            </div>
        </div>
        <!-- Category End -->
        @foreach (App\Sidbar::orderBy('SidebarID', 'ASC')->get() as $sidbar)
        <!-- Recent Post Start -->
        <div class="mb-2" data-wow-delay="0.1s">
            <div class="section-title section-title-sm position-relative pb-2 mb-3">
                <h6 class="mb-0 font-alhuraa">{{ $sidbar->SidebarName }}</h6>
            </div>
            <div class="mb-1 text-center">
                @if (!empty($sidbar->type == 0))
                <p class="mb-0 font-kufi-regular" style="font-size: 12px;">
                    @if (!empty($sidbar->SidebarContent))
                    {!! $sidbar->SidebarContent !!}
                    @endif
                </p>
                @php
                $videolink = $sidbar->SidebarVideo;
                $ytarray = explode("/", $videolink);
                $ytendstring = end($ytarray);
                $ytendarray = explode("?v=", $ytendstring);
                $ytendstring = end($ytendarray);
                $ytendarray = explode("&", $ytendstring);
                $ytcode = $ytendarray[0];
                @endphp
                <div class="fix video-gallery border border-white border-5">
                    <div class="player-small" data-wow-delay="0.1s"
                        data-property="{videoURL:'https://www.youtube.com/embed/{{ $ytcode }}',containment:'self',autoPlay:true, mute:true, startAt:0, opacity:1}">
                    </div>
                </div>
                @elseif (!empty($sidbar->type == 1))
                <p class="mb-0 font-kufi-regular" style="font-size: 12px;">
                    @if (!empty($sidbar->SidebarContent))
                    {!! $sidbar->SidebarContent !!}
                    @endif
                </p>
                <div class="fix video-gallery border border-white border-5 mb-5">
                    <video id="myVideo" width="100%" height="100%" autoplay loop muted>
                        <source src="{{ url('public/videos/' . $sidbar->SidebarVideo) }}" type="video/mp4" />
                    </video>
                </div>
                @elseif (!empty($sidbar->type == 2))
                <div class="mb-5">
                    {!! $sidbar->SidebarContent !!}
                </div>
                @endif
            </div>
        </div>
        @endforeach
        <!-- Recent Post End -->
    </div>
</div>
<!-- Sidebar End -->