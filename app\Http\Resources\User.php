<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class User extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return parent::toArray([
            'ID' => $this->ID,
            'FullName' => $this->FullName,
            'NumberPhone' => $this->NumberPhone,
            'Email' => $this->Email,
            'Password' => $this->Password,
            'Date' => $this->Date,
        ]);
    }
}
