@extends('layout.main')

@section('title', 'استمارة اصدار هوية موظفي ديوان محافظة كركوك')

@section('content')

<div class="container">
	<h2 class="text-center font-alhuraa" style="padding-bottom: 27px;">استمارة اصدار هوية موظفي ديوان محافظة
		كركوك</h2>
	<h5 class="font-kufi-regular text-center">عزيزي الموظف/ ـة معلومات مهمة قبل المباشرة بتحديث بياناتكم</h5>
	<h5 class="font-kufi-regular text-center mb-5">يسمح لكم بتحديث المعلومات لمرة واحدة فقط لذا يرجى توفير
		هذه
		المعلومات قبل الضغط على ارسال</h5>
	<h6 class="font-kufi-regular text-right"><b>صورة شخصية</b> فوتوغراف تسحب على جهاز الماسح الضوئي (Scanner) غير مسموح
		للصورة التي تؤخذ عن طريق الموبايل</h6>
	<div class="font-kufi-regular text-right ">
		<div class="row">

			<div class="col-sm-8 text-center">
				@if (session()->has('success'))
				<div class="alert alert-success text-center">
					{!! session('success') !!}
				</div>
				@endif
				@if (session()->has('successsendid'))
				<div class="alert alert-success text-center">
					{!! session('successsendid') !!}
				</div>
				@endif
				@if (session()->has('erroridentity'))
				<div class="alert alert-danger text-center">
					{!! session('erroridentity') !!}
				</div>
				@endif

				@if (session()->has('errorhaveupdate'))
				<div class="alert alert-warning text-center">
					{!! session('errorhaveupdate') !!}
				</div>
				@endif

				@if (session()->has('errorgetident'))
				<div class="alert alert-danger text-center">
					{!! session('errorgetident') !!}
				</div>
				@endif

				@if (!empty($getIde))
				<h6><strong>{{ $getIde->f3 }}</strong></h6>
				<br />
				@if ($getIde->status == 'قيد المراجعة')
				<p>استمارتكم قيد المراجعة يرجى التحلي بالصبر، بسبب كثرة الطلبات المقدمة لنا وسوف يتم تحديثها في أقرب وقت
					ممكن كما نحثكم الدخول بين فترة وأخرى الى هنا لمتابعة سير طلبكم ربما يوجد في استمارتكم نقص لكي
					تتمكنوا من تحديث بيناتها شاكرين تعاونكم معنا.</p>
				@elseif ($getIde->status == 'قيد الانجاز')
				<h6 style="color: #ef8f23">استمارتكم قيد الإنجاز تم ارسالها الى استحصال موافقة قسم الموارد البشرية وأمن
					المحافظة ومكتب السيد المحافظ وسيتم تحديث المعلومات في حال عودة الاستمارة من هذه الأقسام</h6>
				@elseif ($getIde->status == 'انجزت')
				<p style="color: #8bad00">تم انجاز طلبكم يرجى التوجه الى شعبة اصدار الباجات والهويات في ديوان محافظ
					كركوك لغرض استلامها من قبل مسؤول الشعبة السيد عدي غسان حسين. مع جلب الهوية او الباج القديم ان وجد.
				</p>
				@elseif ($getIde->status == 'نقص')
				<h6 style="color: #0179b8; margin-left: 27px; text-align: justify;">استمارتكم يوجد بها نقص معلومات يرجى
					الاطلاع على النقوصات المكتوبة في الأسفل ومن خلال الدخول الى رابط تعديل الاستمارة. لكي تتمكنوا من
					اكمال النواقص الموجودة. لكي يتسنى لنا اكمال الهوية وباج الدخول الخاص بكم</h6>
				<div class="text-justify" style="margin-left: 27px;">{{ $getIde->getaction }}</div> <br />
				@elseif ($getIde->status == 'رفض')
				<h6 style="color: red; font-weight: bold;">تم رفض طلبكم</h6> <br />
				@endif
				<h6><strong>مع تحيات البوابة الالكترونية لديوان محافظة كركوك</strong></h6>
				@endif
			</div>
		</div>
		<!-- Modal Fetch Id -->
		{{-- <div class="modal fade" id="identityModal" tabindex="-1" aria-labelledby="exampleModalLabel"
			aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="exampleModalLabel">تسجيل دخول</h5>
					</div>
					<div class="modal-body">
						<form action="{{ route('form.selectidentity') }}" method="POST">
							@csrf
							<div class="form-group">
								<label for="recipient-name" class="col-form-label">الاسم الثلاثي:</label>
								<input type="text" name="fullname" class="form-control" id="recipient-name">
							</div>
							<div class="form-group">
								<label for="number-form" class="col-form-label">رقم الاستمارة:</label>
								<input type="text" name="numberform" class="form-control" id="number-form">
							</div>
							<div class="modal-footer">
								<button type="button" style="position: absolute;right: 31px;" class="btn btn-secondary"
									data-dismiss="modal">اغلاق</button>
								<input type="submit" class="btn btn-primary" value="دخول">
							</div>
						</form>
					</div>
				</div>
			</div>
		</div> --}}
		<div class="modal fade" id="ShowMsgModal" tabindex="-1" aria-labelledby="ShowMsgModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">

					</div>
					<div class="modal-body">
						<p>يرجى مراجعة القسم المختص</p>
					</div>
				</div>
			</div>
		</div>
		<!-- Modal Update Id -->
		<div class="modal fade" id="identityUpdateModal" tabindex="-1" aria-labelledby="exampleModalLabel"
			aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="exampleModalLabel">استعلام</h5>
					</div>
					<div class="modal-body">
						<form action="{{ route('form.updateidentity') }}" method="POST">
							@csrf
							<div class="form-group">
								<label for="recipient-name" class="col-form-label">الاسم الثلاثي:</label>
								<input type="text" name="fullname" class="form-control" id="recipient-name">
							</div>
							<div class="form-group">
								<label for="number-form" class="col-form-label">رقم الهوية او الباج:</label>
								<input type="number" name="numberid" class="form-control" id="number-id">
							</div>
							<div class="modal-footer">
								<button type="button" style="position: absolute;right: 31px;" class="btn btn-secondary"
									data-dismiss="modal">اغلاق</button>
								<input type="submit" class="btn btn-primary" value="جلب المعلومات">
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<hr>
		<div class="customNote">
			<p class="font-kufi-regular text-justify text-dark">
				<strong style="color:red;"><em><u>ملاحظة:</u></em></strong>
				<b>
					عند تحميل الصورة الى النظام يرجى وضع الصورة داخل المربع عن طريق الازرار الخاصة بالتكبير والتصغير
					وتحريك
					الصورة الى اليمين او اليسار او الى الأعلى او الأسفل عن طريق الماوس بدون وضع فراغ من الأعلى والاسفل
					ونرجو
					الانتباه الى ان الصورة ستظهر في الهوية كما هي في المربع الظاهر امامك
				</b>
			</p>
			<p class="text-justify">
				<strong style="color:blue;"><em>تعليمات التقديم: </em></strong>
				يرجى اتباع التعليمات وتوفير المتطلبات اللازمة قبل المباشرة بملاء الاستمارة الإلكترونية لتسهيل عملية
				اكمال اصدار الهوية التعريفية ديوان محافظة كركوك.
			</p>
			<p class="text-justify">
				<strong style="color:#000;"><em>الفقرة أولا: </em></strong>
				يجب تهيئة الرقم الوظيفي قبل البدأ
			</p>

			<p class="text-justify">
				<strong style="color:#000;"><em>الفقرة ثانية: </em></strong>
				العنوان الوظيفي للوقت الحالي اذا كان عنوانك الوظيفي ليس من ضمن الخيارات يرجى عدم اكمال عملية التحديث
				والتواصل الدعم الفني عبر البريد <EMAIL>.
			</p>

			<p class="text-justify">
				<strong style="color:#000;"><em>الفقرة ثالثة: </em></strong>
				اسم القسم او الشعبة، اذا كان اسم القسم او الشعبة ليس من ضمن الخيارات يرجى عدم اكمال عملية التحديث
				والتواصل
				الدعم الفني عبر البريد <EMAIL>.
			</p>

			<p class="text-justify">
				<strong style="color:#000;"><em>الفقرة رابعة: </em></strong>
				يرجى تهيئة البيانات التالية:
			<ul>
				<li>رقم البطاقة الوطنية الموحدة</li>
				<li>رقم جواز السفر ان وجد (ملاحظة هو حقل غير الزامي) </li>
				<li>رقم الهاتف يجب ان يكون فيه حساب في تطبيق الواتساب لربطة بنظام المصادقة الثانية</li>
			</ul>
			</p>

			<h5 class="font-kufi-regular text-center">
				طباعة الاستمارة وتصديقها بالتواقيع من قسم الموارد البشرية وتسليمها الى وحدة اصدار الهويات داخل بناية
				ديوان محافظة كركوك
			</h5>
			<hr>
			<div class="text-center">
				<img width="100%" src="{{ url('content/uploads/idefkirkuk.webp') }}">
			</div>
			<div class="col-sm-4 mt-3">
				<ul class="custom-ul list-unstyled pr-0">
					<!-- data-toggle="modal" data-target="#identityModal"  href="#"><img width="180" title="متابعة اصدار قديم" src="" -->
					<li><a target="_blank" href="{{ url('form/identity') }}"><img width="220"
								src="{{ url('content/uploads/newIdef.webp') }}"></a></li>
					{{-- <li><a data-bs-toggle="modal" data-bs-target="#identityModal" href="#"><img width="220"
								src="{{ url('content/uploads/foIdef.webp') }}"></a></li> --}}
					<li><a data-bs-toggle="modal" data-bs-target="#identityUpdateModal" href="#"><img width="220"
								src="{{ url('content/uploads/update.webp') }}"></a></li>
				</ul>
			</div>
			<hr>
			<p class="text-center">
				<strong>ان كنتم تواجهون مشاكل في التقديم او كان لديكم مقترحات يرجى مراسلتنا على البريد
					الالكتروني</strong><br />
				<strong><a href="mailto:<EMAIL>"><EMAIL></a></strong><br />
				<strong>او</strong><br />
				<strong><a href="{{ url('contact-us') }}">اتصل بنا</a></strong><br />
				<strong>مع تحيات البوابة الالكترونية ديوان محافظة كركوك</strong>
			</p>
		</div>
	</div>
</div>
@endsection