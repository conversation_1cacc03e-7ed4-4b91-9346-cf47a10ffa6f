@extends('layout.main')
@section('title', 'دخول الى نظام الشكاوي')
@section('content')
<div class="container">
    <section class="font-kufi-regular">
        <div class="row justify-content-center align-items-center">
            <div class="col-lg-4">
                <div class="text-center">
                    <img src="{{ url('content/uploads/complaints/comlogo.webp') }}" style="width: 367px">
                </div>
            </div>
            <div class="col-lg-4">
                <div style="width: 300px; margin: auto; text-align: right; padding-bottom: 40px;">
                    @if (session()->has('notification'))
                    <div class="alert alert-success text-center">
                        {!! session('notification') !!}
                    </div>
                    @endif
                    <form method="POST" action="{{ route('login') }}">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="username">اسم المستخدم</label>
                            <input id="username" type="text" class="form-control @error('username') is-invalid @enderror" name="username" value="{{ old('username') }}" autocomplete="off" required autofocus>
                            @error('username')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <div class="form-group mb-3">
                            <label for="Password">كلمة السر</label>
                            <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" autocomplete="current-password">
                            @error('password')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <div class="d-grid gap-2 mb-2">
                            <button type="submit" class="btn btn-primary btn-block ">دخول</button>
                        </div>
                    </form>
                    <hr>
                    <div class="d-flex justify-content-between">
                        @if (Route::has('password.request'))
                        <div>
                            {{-- <!-- <a href="{{ route('password.request') }}"> --> --}}
                            <a style="font-size: 12px" href="#">
                                استعادة كلمة المرور
                            </a>
                        </div>
                        @endif
                        <div>
                            <a style="font-size: 12px" class="custom-link" href="{{ url('register') }}">التسجيل في نظام الشكاوي</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection