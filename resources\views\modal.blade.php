<!-- Start OffCanvas CitizenServices -->
<div class="offcanvas offcanvas-bottom citizenservices" tabindex="-1" id="CitizenServices"
    aria-labelledby="CitizenServicesLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title font-alhuraa " id="CitizenServicesLabel">شؤون المواطنين</h5>
        <button type="button" class="btn-close text-reset" style="margin: 0!important;" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>

    </div>
    <div class="offcanvas-body small overflow-hidden">
        <div class="row">
            <div class="col-sm-3">
                <ul class="list-unstyled">
                    <li>
                        <img src="img/complaints.webp" alt="" width="64">
                        <a href="https://ca.iq/" class="btn btn-outline-primary font-alhuraa d-grid me-2">شكاوى
                            المواطنين</a>
                    </li>
                    <li>
                        <img src="img/moving-furniture.webp" alt="" width="64">
                        <a href="{{ url('form/moving_furniture/') }}"
                            class="btn btn-outline-primary font-alhuraa d-grid me-2">استمارة نقل اثاث</a>
                    </li>
                    <li>
                        <img src="img/moving-furniture.webp" alt="" width="64">
                        <a href="{{ url('complaints') }}"
                            class="btn btn-outline-primary font-alhuraa d-grid me-2">الملاحظات والمعوقات الخاصة بنظام
                            الدفع الالكتروني POS</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End OffCanvas CitizenServices -->

<!-- Start OffCanvas StaffServices -->
<div class="offcanvas offcanvas-bottom staffservices" tabindex="-1" id="StaffServices"
    aria-labelledby="StaffServicesLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title font-alhuraa" id="StaffServicesLabel">خدمات الموظفين</h5>
        <button type="button" class="btn-close text-reset" style="margin: 0!important;" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>

    </div>
    <div class="offcanvas-body small overflow-hidden">
        <div class="row">
            <div class="col-sm-5">
                <ul class="list-unstyled font-alhuraa">
                    <li class="mb-2">
                        <a href="{{ url('Email') }}" target="_blank" class="btn btn-outline-primary d-grid me-2">الدخول
                            الى البريد الالكتروني</a>
                    </li>
                    <li class="mb-2">
                        <a href="#" data-bs-toggle="modal" data-bs-target="#recordEmail"
                            class="btn btn-outline-primary d-grid me-2 closeModal">عناوين البريد الالكتروني</a>
                    </li>
                    <li class="mb-2">
                        <a href="{{ route('obtain-email') }}"
                            class="btn btn-outline-primary d-grid me-2 closeModal">استمارة التقديم على البريد
                            الالكتروني</a>
                    </li>
                    <li>
                        <a href="{{ url('form/selectidentity') }}" class="btn btn-outline-primary d-grid me-2">استمارة
                            اصدار هوية موظفي
                            ديوان
                            المحافظة</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End OffCanvas StaffServices -->

<!-- Start OffCanvas Tenders -->
<div class="offcanvas offcanvas-bottom tendersservices" tabindex="-1" id="Tenders" aria-labelledby="TendersLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title font-alhuraa" id="TendersLabel">المناقصات والاعلانات</h5>
        <button type="button" class="btn-close text-reset" style="margin: 0!important;" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>

    </div>
    <div class="offcanvas-body small overflow-scroll">
        <div class="table-responsive font-kufi-bold">
            <table class="table table-hover table-bordered text-center text-white">
                <thead>
                    <tr>
                        <th class="w-25">تاريخ الاعلان</th>
                        <th>عنوان الاعلان</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (\App\Tender::orderBy('TendersID', 'DESC')->paginate(50) as $tender)
                    <tr>
                        <th>{{ $tender->Date }}</th>
                        <td><a class="text-white" href="{{url('tenders') . '/' . $tender->TendersID}}">{{ $tender->Title
                                }}</a></td>
                    </tr>
                    @endforeach
                    <div></div>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- End OffCanvas Tenders -->

<!-- Record Email Modal -->
<div class="modal fade" id="recordEmail" tabindex="-1" aria-labelledby="recordEmailLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title font-alhuraa" id="recordEmailLabel">عناوين البريد الالكتروني</h5>
            </div>
            <div class="modal-body">
                <div class="mailrecorderCustom">
                    <div class="form-group">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="search" id="search" placeholder="بحث في السجل"
                                aria-label="بحث في السجل" aria-describedby="basic-addon2">
                            <span class="input-group-text" id="basic-addon2">بحث في السجل</span>
                        </div>
                    </div>
                    <div class="shadow-lg bg-white rounded font-kufi-regular" id="result"></div>
                    <div style="clear:both"></div>
                </div>
            </div>
        </div>
    </div>
</div>