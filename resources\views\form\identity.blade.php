<!doctype html>
<html lang="ar" dir="rtl">

<head>
	<!-- Required meta tags -->
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="csrf-token" content="{{ csrf_token() }}">
	<!-- Bootstrap CSS -->
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css"
		integrity="sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb" crossorigin="anonymous">
	<link rel="stylesheet" type="text/css" href="{{ asset('public/css/cropper.min.css') }}">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
	<link rel="stylesheet"
		href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.rtl.min.css" />
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css"
		integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg=="
		crossorigin="anonymous" referrerpolicy="no-referrer" />
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.4/jquery-confirm.min.css">

	<link rel="shortcut icon" href="{{ asset('public/img/favicon.ico') }}">
	<title>استمارة اصدار هوية موظفي وباج دخول ديوان محافظة كركوك</title>
	<style>
		@import url(//fonts.googleapis.com/earlyaccess/notokufiarabic.css);

		body {
			font-family: "Noto Kufi Arabic", sans-serif;
		}

		label.is-invalid {
			display: none !important;
		}

		.input-group-text {
			padding: 0.275rem .75rem !important;
		}

		.jconfirm .jconfirm-box div.jconfirm-content-pane .jconfirm-content {
			overflow: hidden;
		}

		.error {
			font-size: 12px;
			font-weight: bold;
			color: #f00;
		}

		#f10-error {
			font-size: 12px;
			font-weight: bold;
			color: #f00;
			display: inline-block !important;
			width: 100%
		}

		.jconfirm .jconfirm-box div.jconfirm-content-pane .jconfirm-content {
			color: #077737;
			text-align: justify;
		}
	</style>
</head>

<body>
	<div class="modal fade" id="CropModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
		aria-labelledby="CropModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-xl">
			<div class="modal-content">
				<div class="modal-header">
					<h1 class="modal-title fs-5" id="CropModalLabel">اقتصاص الصورة</h1>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div class="cropimg" style="direction: ltr;">
						<div class="row">
							<div class="col-md-8">
								<div style="min-height: 497px;max-height: 497px">
									<img src="" id="sample_image" />
								</div>

							</div>
							<div class="col-md-4 text-center">
								<div class="preview"></div>
								<div class="btn-group mt-5" style="display: inherit!important;">
									<button type="button" class="btn btn-primary" title="Zoom In" id="Zoom_In">
										<span>
											<span class="fa fa-search-plus"></span>
										</span>
									</button>
									<button type="button" class="btn btn-primary" title="Zoom Out" id="Zoom_Out">
										<span>
											<span class="fa fa-search-minus"></span>
										</span>
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" id="crop">اقتصاص</button>
				</div>
			</div>
		</div>
	</div>
	<div class="container">
		<h4 class="text-center mt-3">استمارة اصدار هوية موظفي وباج دخول ديوان محافظة كركوك</h4>
		<hr>
		<form data-url="{{ route('form.identity.store') }}" id="form-identity" method="POST"
			enctype="multipart/form-data">
			@csrf
			<div class="d-flex flex-column align-items-center mb-2 uploaded_image">
				<label for="upload_image" class="btn btn-outline-secondary mb-2">الصورة الشخصية</label>
				<input type="file" class="d-none" id="upload_image" data-type="upload1" required>
				<input type="hidden" id="upload1" name="f2">
				<img style="width: 150px!important;" id="uploaded_image" src="">
			</div>
			<div class="row">
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">الحالة</span>
						<select class="form-control" name="f1" required>
							<option value="موظف ملاك" {{ old('f1')=='موظف ملاك' ? 'selected' : '' }}>موظف ملاك</option>
							<option value="باج دخول ديوان محافظة كركوك" {{ old('f1')=='باج دخول ديوان محافظة كركوك'
								? 'selected' : '' }}>باج دخول ديوان محافظة كركوك</option>
							<option value="اخرى" {{ old('f1')=='اخرى' ? 'selected' : '' }}>اخرى</option>
						</select>
					</div>
				</div>
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">الاسم الثلاثي</span>
						<input type="text" name="f3" value="{{ old('f3') }}" class="form-control" required>
					</div>
				</div>
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">الاسم باللغة الانكليزية</span>
						<input type="text" name="f4" value="{{ old('f4') }}" pattern="\S(.*\S)?" class="form-control"
							onkeyup="this.value = this.value.toUpperCase();" required>
					</div>
				</div>
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">رقم الهاتف</span>
						<input type="number" name="f10" value="{{ old('f10') }}" class="form-control" minlength="11"
							maxlength="11" required>
					</div>
				</div>
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">الحالة الزوجية</span>
						<select name="f1new" class="form-control" required>
							<option value="">اختر...</option>
							<option value="متزوج">متزوج</option>
							<option value="اعزب">اعزب</option>
							<option value="ارمل">ارمل</option>
							<option value="مطلق">مطلق</option>
						</select>
					</div>
				</div>
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">سنة التولد</span>
						<select name="f2new" class="form-control" required>
							<option value="">اختر...</option>
							@for ($i = date("Y") - 100; $i <= date("Y") - 18; $i++) <option value="{{$i}}">{{$i}}
								</option>
								@endfor
						</select>
					</div>
				</div>
			</div>
			<hr>
			<div class="customInput1">
				<div class="row">
					<div class="col-4">
						<div class="input-group mb-3">
							<span class="input-group-text">الرقم الوظيفي</span>
							<input type="text" name="job_number" value="{{ old('job_number') }}" class="form-control"
								required>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-6">
						<div class="input-group mb-3">
							<span class="input-group-text">العنوان
								الوظيفي</span>
							<select name="f5" class="display-results" data-searchable='searchable' required>
								<option value="">اختر...</option>
								@foreach (\Illuminate\Support\Facades\DB::table('jobs_and_section_title')
								->where('type', 1)->get() as $item)
								<option value="{{$item->title}}">{{$item->title}}</option>
								@endforeach
							</select>
						</div>
					</div>
					<div class="col-6">
						<div class="input-group mb-3">
							<span class="input-group-text">القسم او
								الشعبة</span>
							<select name="f6" class="display-results" data-searchable='searchable' required>
								<option value="">اختر...</option>
								@foreach (\Illuminate\Support\Facades\DB::table('jobs_and_section_title')
								->where('type', 3)->get() as $item)
								<option value="{{$item->title}}">{{$item->title}}</option>
								@endforeach
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="customInput2 d-none">
				<div class="row">
					<div class="col-4">
						<div class="input-group mb-3">
							<span class="input-group-text">موظف</span>
							<select class="form-control" name="ff6">
								<option value="">اختر...</option>
								<option value="لا">لا</option>
								<option value="نعم">نعم</option>
							</select>
						</div>
					</div>
					<div class="col-4 inputsjobs2 d-none">
						<div class="input-group mb-3">
							<span class="input-group-text">اسم الدائرة</span>
							<input type="text" name="ff8" class="form-control">
						</div>
					</div>
					<div class="col-4 inputsjobs d-none">
						<div class="input-group mb-3">
							<span class="input-group-text">المهنة</span>
							<input type="text" name="ff5" class="form-control">
						</div>
					</div>
					<div class="col-4 inputsjobs d-none">
						<div class="input-group mb-3">
							<span class="input-group-text">مكان العمل</span>
							<input type="text" name="ff9" class="form-control">
						</div>
					</div>
				</div>
			</div>
			<div class="customInput3 d-none">
				<div class="row">
					<div class="col-6">
						<div class="input-group mb-3">
							<span class="input-group-text">العنوان
								الوظيفي</span>
							<select name="f5" class="display-results" data-searchable='searchable' required>
								<option value="">اختر...</option>
								@foreach (\Illuminate\Support\Facades\DB::table('jobs_and_section_title')
								->where('type', 1)->get() as $item)
								<option value="{{$item->title}}">{{$item->title}}</option>
								@endforeach
							</select>
						</div>
					</div>
					<div class="col-6">
						<div class="input-group mb-3">
							<span class="input-group-text">القسم او
								الشعبة</span>
							<select name="f6" class="display-results" data-searchable='searchable' required>
								<option value="">اختر...</option>
								@foreach (\Illuminate\Support\Facades\DB::table('jobs_and_section_title')
								->where('type', 3)->get() as $item)
								<option value="{{$item->title}}">{{$item->title}}</option>
								@endforeach
							</select>
						</div>
					</div>
				</div>
			</div>
			<hr>
			<div class="row">
				<div class="col-3">
					<div class="input-group mb-3">
						<span class="input-group-text">رقم البطاقه الوطنية</span>
						<input type="text" class="form-control" name="f3new" required>
					</div>
				</div>
				<div class="col-3">
					<div class="input-group mb-3">
						<span class="input-group-text">محل اصدارها</span>
						<select class="form-control" name="f4new" required>
							<option value="">اختر...</option>
							<option value="كركوك">كركوك</option>
							<option value="بغداد">بغداد</option>
							<option value="البصرة">البصرة</option>
							<option value="ميسان">ميسان</option>
							<option value="ذي قار">ذي قار</option>
							<option value="الديوانية">الديوانية</option>
							<option value="المثنى">المثنى</option>
							<option value="النجف الاشرف">النجف الاشرف</option>
							<option value="كربلاء المقدسة">كربلاء المقدسة</option>
							<option value="بابل">بابل</option>
							<option value="واسط">واسط</option>
							<option value="ديالى">ديالى</option>
							<option value="صلاح الدين">صلاح الدين</option>
							<option value="نينوى">نينوى</option>
							<option value="الانبار">الانبار</option>
							<option value="اربيل">اربيل</option>
							<option value="دهوك">دهوك</option>
							<option value="سليمانية">سليمانية</option>
						</select>
					</div>
				</div>
				<div class="col-3">
					<div class="input-group mb-3">
						<span class="input-group-text">تاريخ اصدارها</span>
						<input type="date" class="form-control" name="f5new" required>
					</div>
				</div>
				<div class="col-3">
					<div class="input-group mb-3">
						<span class="input-group-text">رقم الجواز</span>
						<input type="text" class="form-control" name="f6new">
					</div>
				</div>
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">عنوان السكن</span>
						<input type="text" class="form-control" name="f13" required>
					</div>
				</div>
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">القضاء</span>
						<input type="text" class="form-control" name="f14" required>
					</div>
				</div>
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">الناحية</span>
						<input type="text" class="form-control" name="f15" required>
					</div>
				</div>
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">الحي او القرية</span>
						<input type="text" class="form-control" name="f16" required>
					</div>
				</div>
				<div class="col-8">
					<div class="input-group mb-3">
						<span class="input-group-text">اقرب نقطة دالة</span>
						<input type="text" class="form-control" name="f19" required>
					</div>
				</div>
			</div>
			<hr>
			<div class="row">
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">هل تملك مركبة خاصة او حكومية</span>
						<select class="form-control" name="f20" required>
							<option value="لا">لا</option>
							<option value="نعم">نعم</option>
						</select>
					</div>
				</div>
			</div>
			<div class="row apeendDetailsCar">

			</div>
			<div class="row">
				<div class="col-4">
					<div class="input-group mb-3">
						<span class="input-group-text">هل تملك سلاح</span>
						<select class="form-control" name="f27" required>
							<option value="لا">لا</option>
							<option value="نعم">نعم</option>
						</select>
					</div>
				</div>
			</div>
			<div class="row apeendWeponsCar">

			</div>
			<hr>
			<div style="width: 300px; margin: 0 auto!important">
				<div class="form-group">
					{!! NoCaptcha::renderJs() !!}
					{!! NoCaptcha::display() !!}
					<span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
				</div>
			</div>
			<div class="text-center mt-2 mb-5">
				<button type="submit" class="btn btn-primary btn-send">ارسال</button>
			</div>
		</form>
	</div>
	<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
		integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
	</script>
	<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
	<script src="{{ asset('public/js/cropper.min.js') }}"></script>
	<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/js/all.min.js"
		integrity="sha512-6sSYJqDreZRZGkJ3b+YfdhB3MzmuP9R7X1QZ6g5aIXhRvR1Y/N/P47jmnkENm7YL3oqsmI6AK+V6AD99uWDnIw=="
		crossorigin="anonymous" referrerpolicy="no-referrer"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.4/jquery-confirm.min.js"></script>
	<script>
		$(function() {
			"use strict";
			$.ajaxSetup({
				headers: {
					"X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
				},
			});
			// Searchable Library
			$("body [data-searchable='searchable']").select2({
				dir: 'rtl',
				theme: 'bootstrap-5',
				language: 'ar',
				placeholder: 'اختر...',
			});

			// Crop Modal
			var $modal = $('#CropModal');

			var image = document.getElementById('sample_image');

			var cropper;

			$('#upload_image').change(function(event) {
				var files = event.target.files;
				var thisinput = $(this);
				var done = function(url) {
					image.src = url;
					$modal.modal('show');
					$("#crop").attr('data-upload-image', thisinput.attr('data-type'));
				};

				if (files && files.length > 0) {
					var reader = new FileReader();
					reader.onload = function(event) {
						done(reader.result);
					};
					reader.readAsDataURL(files[0]);
				}
			});

			$modal.on('shown.bs.modal', function() {
				cropper = new Cropper(image, {
					dragMode: 'move',
					aspectRatio: 2.5 / 3.0,
					autoCropArea: 0.65,
					restore: false,
					guides: false,
					center: false,
					highlight: false,
					cropBoxMovable: false,
					cropBoxResizable: false,
					toggleDragModeOnDblclick: false,
				});
			}).on('hidden.bs.modal', function() {
				cropper.destroy();
				cropper = null;
			});

			$('#crop').click(function() {
				var dataUploadImage = $(this).attr('data-upload-image');
				var canvas = cropper.getCroppedCanvas({
					width: 400,
					height: 600
				});
				canvas.toBlob(function(blob) {
					var url = URL.createObjectURL(blob);
					var reader = new FileReader();
					reader.readAsDataURL(blob);
					reader.onloadend = function() {
						var base64data = reader.result;
						$('#uploaded_image').attr('src', base64data);
						$("#upload1").val(base64data);

						$modal.modal('hide');
					};
				});
			});
			$('#Zoom_In').click(function() {
				cropper.zoom(0.1);
			});
			$('#Zoom_Out').click(function() {
				cropper.zoom(-0.1);
			});
			// Jquery Validate
			$('#form-identity').validate({
				errorClass: "is-invalid",
				validClass: "is-valid",
				ignoreTitle: true,
				invalidHandler: function(event, validator) {
					grecaptcha.reset();
				},
			});
			$.extend($.validator.messages, {
				required: 'الحقل مطلوب',
				minlength: 'يجب ان يكون رقم الهاتف 11 رقم',
				maxlength: 'يجب ان يكون رقم الهاتف 11 رقم',
				accept: 'تنسيق الملف غير مدعوم!',
			});

			$("#form-identity").on('submit', function(e) {
				e.preventDefault();
				if($('#upload1').val() == '') {
						$('.uploadimageerror').remove();
						$('.uploaded_image').append('<label class="error uploadimageerror">الصورة الشخصية مطلوبة</label>');
						$.alert({
							title: 'تحذير!',
							content: 'يرجى رفع صورة شخصية!',
							rtl: true,
							closeIcon: true,
							type: 'red',
							typeAnimated: true,
						});
						return false;
				}
					var formData = new FormData(this);
					$.ajax({
						data: formData,
						url: $("#form-identity").attr("data-url"),
						method: "POST",
						dataType: "json",
						contentType: false,
						cache: false,
						processData: false,
						beforeSend: function () {
							// setting a timeout
							$(".btn-send").html(
								'<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span><span class="visually-hidden">Loading...</span>'
							);
							$(".btn-send").attr("disabled", "disabled");
						},
						success: function (data) {
							if (data["status"] == "success") {
								$.confirm({
									title: 'تم الارسال بنجاح!',
									content: data["successsend"],
									type: 'green',
									rtl: true,
									typeAnimated: true,
									buttons: {
										اغلاق: function () {
											location.reload();
										}
									}
								});
							}
						},
						error: function (data) {
							console.log(data);
						},
						complete: function () {
							$(".btn-send").html("اضافة");
							$(".btn-send").removeAttr("disabled");
						},
					});
				
			});
			$('[name="f1"]').on('change', function() {
				$(".customInput1 input").removeAttr('required');
				$(".customInput1 select").removeAttr('required');
				$(".customInput1").addClass('d-none');
				$(".customInput2").addClass('d-none');
				$(".customInput2 input").removeAttr('required');
				$(".customInput2 select").removeAttr('required');
				$(".customInput3 select").removeAttr('required');
				$(".customInput3").addClass('d-none');
				$('[name="job_number"]').val('');
				$('[name="ff5"]').val('');
				$('[name="ff8"]').val('');
				$('[name="ff9"]').val('');
				$(".customInput1 select").val(null).trigger('change');
				$(".customInput3 select").val(null).trigger('change');
				$('[name="ff6"]').prop('selectedIndex',0);
				$('.inputsjobs').addClass('d-none');
				$('.inputsjobs input').removeAttr('required');
				$('.inputsjobs2').addClass('d-none');
				$('.inputsjobs2 input').removeAttr('required');
				for (let i = 1; i <= 3; i++) {
					$(".customInput"+i+" input").removeClass('is-valid');
					$(".customInput"+i+" select").removeClass('is-valid');
					$(".customInput"+i+" input").removeClass('is-invalid');
					$(".customInput"+i+" select").removeClass('is-invalid');
				}
				if ($(this).val() == 'موظف ملاك') {
					$(".customInput1 input").attr('required', 'required');
					$(".customInput1 select").attr('required', 'required');
					$(".customInput1").removeClass('d-none');
				} else if ($(this).val() == 'باج دخول ديوان محافظة كركوك') {
					$(".customInput2 input").attr('required', 'required');
					$(".customInput2 select").attr('required', 'required');
					$(".customInput2").removeClass('d-none');
				} else if ($(this).val() == 'اخرى') {
					$(".customInput3 select").attr('required', 'required');
					$(".customInput3").removeClass('d-none');
				}
			});
			$(document).on('change', '[name="ff6"]', function() {
				$('.inputsjobs').addClass('d-none');
				$('.inputsjobs input').removeAttr('required');
				$('.inputsjobs2').addClass('d-none');
				$('.inputsjobs2 input').removeAttr('required');
				if ($(this).val() == 'نعم') {
					$('.inputsjobs2').removeClass('d-none');
					$(".inputsjobs2 input").attr('required', 'required');
				 } else if ($(this).val() == 'لا') {
					$('.inputsjobs').removeClass('d-none');
					$(".inputsjobs input").attr('required', 'required');
				 } 
			});
			$('[name="f20"]').on('change', function() {
				$(".apeendDetailsCar").html('');
				if ($(this).val() == 'نعم') {
					var html = '<div class="col-4">'+
					'<div class="input-group mb-3">'+
						'<span class="input-group-text">اسم صاحب السنوية</span>'+
						'<input type="text" class="form-control" name="f22" required>'+
					'</div>'+
				'</div>'+
				'<div class="col-2">'+
					'<div class="input-group mb-3">'+
						'<span class="input-group-text">رقمها</span>'+
						'<input type="text" class="form-control" name="f23" required>'+
					'</div>'+
				'</div>'+
				'<div class="col-2">'+
					'<div class="input-group mb-3">'+
						'<select class="form-control" name="f24" required>'+
							'<option value="">المحافظة</option>'+
							'<option value="حكومي">حكومي</option>'+
							'<option value="منفيس">منفيس</option>'+
							'<option value="كركوك">كركوك</option>'+
							'<option value="بغداد">بغداد</option>'+
							'<option value="البصرة">البصرة</option>'+
							'<option value="ميسان">ميسان</option>'+
							'<option value="ذي قار">ذي قار</option>'+
							'<option value="الديوانية">الديوانية</option>'+
							'<option value="المثنى">المثنى</option>'+
							'<option value="النجف الاشرف">النجف الاشرف</option>'+
							'<option value="كربلاء المقدسة">كربلاء المقدسة</option>'+
							'<option value="بابل">بابل</option>'+
							'<option value="واسط">واسط</option>'+
							'<option value="ديالى">ديالى</option>'+
							'<option value="صلاح الدين">صلاح الدين</option>'+
							'<option value="نينوى">نينوى</option>'+
							'<option value="الانبار">الانبار</option>'+
							'<option value="اربيل">اربيل</option>'+
							'<option value="دهوك">دهوك</option>'+
							'<option value="سليمانية">سليمانية</option>'+
						'</select>'+
					'</div>'+
				'</div>'+
				'<div class="col-2">'+
					'<div class="input-group mb-3">'+
						'<span class="input-group-text">نوعها</span>'+
						'<input type="text" class="form-control" name="f25" required>'+
					'</div>'+
				'</div>'+
				'<div class="col-2">'+
					'<div class="input-group mb-3">'+
						'<span class="input-group-text">موديلها</span>'+
						'<input type="text" class="form-control" name="f26" required>'+
					'</div>'+
				'</div>';
					$(".apeendDetailsCar").html(html);
				}
			});
			$('[name="f27"]').on('change', function() {
				$(".apeendWeponsCar").html('');
				if ($(this).val() == 'نعم') {
					var html = '<div class="col-3">'+
					'<div class="input-group mb-3">'+
						'<span class="input-group-text">نوع السلاح</span>'+
						'<input type="text" class="form-control" name="f29" required>'+
					'</div>'+
				'</div>'+
				'<div class="col-3">'+
					'<div class="input-group mb-3">'+
						'<span class="input-group-text">رقم السلاح</span>'+
						'<input type="text" class="form-control" name="f30" required>'+
					'</div>'+
				'</div>'+
				'<div class="col-3">'+
					'<div class="input-group mb-3">'+
						'<span class="input-group-text">رقم الرخصة</span>'+
						'<input type="text" class="form-control" name="f31" required>'+
					'</div>'+
				'</div>'+
				'<div class="col-3">'+
					'<div class="input-group mb-3">'+
						'<span class="input-group-text">التاريخ</span>'+
						'<input type="date" class="form-control" name="f32" required>'+
					'</div>'+
				'</div>';
					$(".apeendWeponsCar").html(html);
				}
			});

		});
	</script>
</body>

</html>