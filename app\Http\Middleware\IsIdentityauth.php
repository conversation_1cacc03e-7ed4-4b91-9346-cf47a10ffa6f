<?php

namespace App\Http\Middleware;

use Closure;
use App\Identity;
class IsIdentityauth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        if($request->fullname == Identity::first()->f3 && $request->numberform == Identity::first()->id){
            return $next($request);
        } else {
           return view('form/selectidentity')->with('error','You don\'t have admin access.'); 
        }
    }
}
