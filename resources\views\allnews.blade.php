@extends('layout.main')
@section('title', 'اعلام المحافظة')
@section('content')

<div class="container">
	<div class="row">
		@foreach ($allnews as $new)
		<div class="col-md-4 mb-4" data-wow-delay="0.1s">
			<div class="blog-item bg-light rounded overflow-hidden">
				<div class="blog-img position-relative overflow-hidden">
					@if ($new->Dbvanews == 0)
					<img class="img-fluid" src="{{ url('content/uploads') . '/' . $new->Image }}"
						alt="{{ $new->Title }}">
					@elseif ($new->Dbvanews == 1)
					<div class="fix video-gallery">
						<div class="player-small" data-wow-delay="0.1s"
							data-property="{videoURL:'{{$new->Video}}',containment:'self',autoPlay:true, mute:true, startAt:0, opacity:1}">
						</div>
					</div>
					@elseif ($new->Dbvanews == 2)
					<iframe src="https://www.facebook.com/plugins/video.php?href={{$new->VideoFacebook}}" width="500"
						height="283" style="border: none; overflow: hidden;width: 100%;height: 15vw;" scrolling="no"
						frameborder="0" allowfullscreen="true"
						allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
						allowfullscreen="true"></iframe>
					@endif

					{{-- <a class="position-absolute top-0 start-0 bg-primary text-white rounded-end mt-5 py-2 px-4"
						href="">Web Design</a> --}}
				</div>
				<div class="p-4">
					<div class="d-flex mb-3">
						@if (!empty($new->Count_View))
						<small><i class="fas fa-eye"></i> {{ $new->Count_View }}</small>
						@else
						<small>عدد القراءات : لاتوجد مشاهدات حالياً</small>
						@endif

						<small><i class="far fa-calendar-alt text-primary me-2"></i> {{ $new->Date }}</small>
					</div>
					<h6 class="mb-3 font-kufi-bold"><a href="news/{{ $new->NewsID }}">{{ implode(' ',
							array_slice(explode(' ', strip_tags($new->Title)), 0, 10)) }}...</a></h6>
					<p class="font-kufi-regular fs-custom">{{ implode(' ', array_slice(explode(' ',
						strip_tags($new->Content)), 0, 20)) }}...<a class="text-uppercase"
							href="news/{{ $new->NewsID }}">قراءة المزيد... <i class="bi bi-arrow-right"></i></a></p>
				</div>
			</div>
		</div>
		@endforeach
	</div>
	{{ $allnews->onEachSide(1)->links() }}
</div>
@endsection