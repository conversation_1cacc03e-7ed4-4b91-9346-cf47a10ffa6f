@extends('layout.mainkirkukcouncil')

@section('title', 'نافذة اصدار هويات - مجلس محافظة كركوك')

@section('content')
    @if (session('kirkukprovincialcouncilpassword') == true)
    <div class="container">
        <div class="row align-items-center">

            <div class="col-sm-6">
<div class="dailyui">
      <div id="logo"><h1 class="logo">hulu</h1>
    <div class="CTA">
      <h1>Get $10</h1>
      </div>
  </div>
  <div class="leftbox">
    <nav>
      <a id="profile" class="active"><i class="fa fa-user"></i></a>
      <a id="payment"><i class="fa fa-credit-card"></i></a>
      <a id="subscription"><i class="fa fa-tv"></i></a>
      <a id="privacy"><i class="fa fa-tasks"></i></a>
      <a id="settings"><i class="fa fa-cog"></i></a>
    </nav>
  </div>
  <div class="rightbox">
    <div class="profile">
      <h1>Personal Info</h1>
      <h2>Full Name</h2>
      <p>Julie Park <button class="btn">update</button></p>
      <h2>Birthday</h2>
      <p>July 21</p>
      <h2>Gender</h2>
      <p>Female</p>
      <h2>Email</h2>
      <p><EMAIL> <button class="btn">update</button></p>
      <h2>Password </h2>
      <p>••••••• <button class="btn">Change</button></p>
    </div>
    
    <div class="payment noshow">
      <h1>Payment Info</h1>
      <h2>Payment Method</h2>
      <p>Mastercard •••• •••• •••• 0000 <button class="btn">update</button></p>
      <h2>Billing Address</h2>
      <p>1234 Example Ave | Seattle, WA <button class="btn">change</button></p>
      <h2>Zipcode</h2>
      <p>999000</p>
      <h2>Billing History</h2>
      <p>2018<button class="btn">view</button></p>
      <h2>Redeem Gift Subscription </h2>
      <p><input type="text" placeholder="Enter Gift Code"></input> <button class="btn">Redeem</button></p>
    </div>

    <div class="subscription noshow">
      <h1>Your Subscription</h1>
      <h2>Payment Date</h2>
      <p>05-15-2018 <button class="btn">pay now</button></p>
      <h2>Your Next Charge</h2>
      <p>$8.48<span> includes tax</span></p>
      <h2>Hulu Base Plan</h2>
      <p>Limited Commercials <button class="btn">change plan</button></p>
      <h2>Add-ons</h2>
      <p>None <button class="btn">manage</button></p>
      <h2>Monthly Recurring Total </h2>
      <p>$7.99/month</p>
    </div>

    <div class="privacy noshow">
      <h1>Privacy Settings</h1>
      <h2>Manage Email Notifications<button class="btn">manage</button></h2>
      <p></p>
      <h2>Manage Privacy Settings<button class="btn">manage</button></h2>
      <p></p>
      <h2>View Terms of Use <button class="btn">view</button></h2>
      <p></p>
      <h2>Personalize Ad Experience <button class="btn">update</button></h2>
      <p></p>
      <h2>Protect Your Account <button class="btn">protect</button></h2>
      <p></p>
    </div>
 <div class="settings noshow">
      <h1>Account Settings</h1>
      <h2>Sync Watchlist to My Stuff<button class="btn">sync</button></h2>
      <p></p>
      <h2>Hold Your Subscription<button class="btn">hold</button></h2>
      <p></p>
      <h2>Cancel Your Subscription <button class="btn">cancel</button></h2>
      <p></p>
      <h2>Your Devices <button class="btn">Manage Devices</button></h2>
      <p></p>
      <h2>Referrals <button class="btn">get $10</button></h2>
   <p></p>
    </div>
    
  </div>
</div>
            </div>
            <div class="col-sm-6">
                <header>
                    <img class="img-fluid" src="{{ asset('public/kirkukprovincialcouncil/asset/img/logo.webp') }}" alt="Logo">
                </header>
                <div class="main">
                    <div class="mt-5 button-form">
                        <a href="{{ route('form.kirkukprovincialcouncil.identity') }}" target="_blank" class="btn btn-primary">التقديم على استمارة الهويات</a>
                    </div>
                </div>
            </div>
        </div>

        {{-- <div class="main-table mt-4">
            <table class="table table-bordered table-hover text-center" style="font-size: 12px;">
                <thead class="bg-dark text-white">
                    <tr class="align-middle"> 
                        <th scope="col">التاريخ</th>
                        <th scope="col">رقم الطلب</th>
                        <th scope="col">الاسم</th>
                        <th scope="col">الاجراء</th>
                        <th scope="col">رقم الهوية</th>
                        <th scope="col">الادارة</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($identitys as $identity)
                    <tr class="align-middle">
                        <th>{{date('Y-m-d', strtotime($identity->created_at))}}</th>
                        <th>{{$identity->id}}</th>
                        <th>{{$identity->f3}}</th>
                        <th>
                            <div class="row">
                        @switch($identity->status)
                        
                            @case('قيد الانجاز')
                                <div class="col-sm-4"><i style="color:#ffc107" class="fas fa-hourglass-end fa-2x"></i></div>
                                <div class="col-sm-6" style="color:#ffc107">الاستمارة قيد الانجاز</div>
                                @break
                            @case('انجزت')
                                <div class="col-sm-4"><i style="color:#28a745" class="fas fa-check-square fa-2x"></i></div>
                                <div class="col-sm-6" style="color:#28a745">تـــــم طبــــع الهـويــــة</div>
                                @break
                            @case('تجديد')
                                <div class="col-sm-4"><i style="color:#17a2b8" class="fas fa-sync-alt fa-2x"></i></div>
                                <div class="col-sm-6" style="color:#17a2b8">طلب تجديد الهوية</div>
                                @break
                            @case('نقص')
                                <div class="col-sm-4"><i style="color:#f00" class="fas fa-syncsdsdsd-alt fa-2x"></i></div> 
                                <div class="col-sm-6" style="color:#f00">نقص بيانات الاستمارة</div>
                                @break
                            @case('رفض')
                                <div class="col-sm-4"><i style="color:#f00" class="fas fa-window-close fa-2x"></i></div> 
                                <div class="col-sm-6" style="color:#f00">تم رفض الاستمارة</div>
                                @break
                            @default
                                <div class="col-sm-4"><i style="color:rgb(53, 53, 53)" class="far fa-clock fa-2x"></i></div> 
                                <div class="col-sm-6" style="color:rgb(53, 53, 53)">قــــيـــد الــمـراجـــــعة</div>
                        
                        @endswitch
                        </div>
                        </th>
                        <th></th>
                        <th>
                            <a href="#" class="btn btn-primary btn-sm" title="معاينة"><i class="fas fa-eye"></i></a>
                            @if ($identity->status == 'نقص')
                                <a href="{{route('kirkukprovincialcouncil.edit', $identity->id)}}" class="btn btn-info btn-sm" title="تعديل"><i class="fas fa-edit"></i></a>
                            @endif
                            @if (empty($identity->getaction))
                                <a href="#" class="btn btn-secondary btn-sm" title="رسالة"><i class="fas fa-envelope-open-text"></i></a>
                            @else
                                <a data-bs-toggle="modal" data-bs-target="#getactionModal" style="cursor: pointer" class="btn btn-success btn-sm animate__animated animate__flash animate__infinite" title="رسالة"><i class="fas fa-envelope-open-text"></i></a>
                                <!-- Modal -->
                                <div class="modal fade" id="getactionModal" tabindex="-1" aria-labelledby="getactionModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="getactionModalLabel">{{$identity->f3}}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        {{$identity->getaction}}
                                    </div>
                                    </div>
                                </div>
                                </div>
                            @endif
                            
                        </th>
                    </tr> 
                    @endforeach
                </tbody>
            </table>
            {{ $identitys->links() }}
        </div> --}}
    </div>
    @else
        <div class="login">
        <div class="form">
            <form class="login-form" action="{{ route('kirkuk.provincial.council.password') }}" method="POST">
                @csrf

                <input type="password" name="password" class="form-control @if (session('errorkirkukprovincialcouncilpassword')) is-invalid @endif" placeholder="كلمة السر" required />
                @if (session('errorkirkukprovincialcouncilpassword'))
                    <div class="invalid-feedback">
                        {{ session('errorkirkukprovincialcouncilpassword') }}
                    </div>
                @endif
                <button>دخول</button>
            </form>  
        </div>
        </div>
    @endif
@endsection