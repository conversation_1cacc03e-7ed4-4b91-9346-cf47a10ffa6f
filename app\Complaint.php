<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\User;
use App\ActionComplaint;
use App\Comment;

class Complaint extends Model
{
    protected $table = 'complaints';
    protected $primaryKey = 'id';
    protected $fillable = [
        'subject',
        'phone',
        'company',
        'content',
        'file1',
        'file2',
        'user_id',
        'statuscomment',
        'statuscom',
        'readablecomplaints',
    ];
    const CREATED_AT = 'date';
    const UPDATED_AT = 'updated_at';
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function action()
    {
        return $this->hasMany(ActionComplaint::class);
    }
    public function comment()
    {
        return $this->hasMany(Comment::class);
    }
}
