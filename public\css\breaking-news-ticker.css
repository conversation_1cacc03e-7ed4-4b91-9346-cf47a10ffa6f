.breaking-news-ticker{
  display: block;
  width: 100%;
  border: solid 1px #ce2525;
  background: #FFF;
  height: 40px;
  box-sizing: border-box;
  position: relative;
  line-height: 40px;
  overflow: hidden;
  border-radius: 2px;
  text-align: auto;
  font-size: 14px;
}
.breaking-news-ticker *{
  box-sizing: border-box;
}
.breaking-news-ticker.bn-fixed-top{
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
}
.breaking-news-ticker.bn-fixed-bottom{
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
}





/*********************************/
/*title styles start**************/
.bn-label{
  left: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  position: absolute;
  background-color: #ce2525;
  text-align: center;
  color: #FFF;
  font-weight: bold;
  z-index: 3;
  padding: 0 15px;
  white-space: nowrap;
}
/*title styles end****************/



/*********************************/
/*news item style start **********/
.bn-news{
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  right: 0;
  overflow: hidden;
}
.bn-news ul{
  display: block;
  height: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  position: absolute;
}
.bn-news ul li{
  white-space: nowrap;
  overflow:hidden !important;
  text-overflow: ellipsis;
  text-decoration: none;
  -webkit-transition: color .2s linear;
  -moz-transition: color .2s linear;
  -o-transition: color .2s linear;
  transition: color .2s linear;
  position: absolute;
  width: 100%;
  display: none;
  color: #333;
}
.bn-news ul li a{
  white-space: nowrap;
  text-overflow: ellipsis;
  text-decoration: none;
  padding: 0 10px;
  color: #333;
  position: relative;
  display: block;
  overflow: hidden;
}
.bn-news ul li a:hover{
  color: #069;
}
.bn-loader-text{
  padding: 0 10px;
}
.bn-seperator{
  display: inline-block;
  float: left;
  margin-right: 15px;
  width: 30px;
  height: 40px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;
}
.bn-seperator.bn-news-dot{
  margin-right: 0;
}
.bn-seperator.bn-news-dot:after{
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background: #333;
  border-radius: 50%;
  top: 50%;
  margin-top: -4px;
  left: 50%;
  margin-left: -4px;
}
.bn-prefix{
  color: #d65d7a;
  margin-right: 15px;
  padding-left: 10px;
}
.bn-positive{
  color: #0b8457;
  font-weight: bold;
}
.bn-negative{
  color: #dc2f2f;
  font-weight: bold;
}
/*news item style end ************/



/*********************************/
/*controls style start ***********/
.bn-controls{
  width: auto;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  position: absolute;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.bn-controls button{
  width: 30px;
  float: left;
  height: 100%;
  cursor: pointer;
  border:none;
  border-left: solid 1px #EEE;
  text-align: center;
  background-color: #f6f6f6;
  outline: none;
}
.bn-controls button:hover{
  background-color: #EEE;
}

.bn-arrow {
  margin: 0;
  display: inline-block;
  height: 8px;
  position: relative;
  width: 8px;
  top: -2px;
}
.bn-arrow::after {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-right-style: solid;
  border-right-width: 2px;
  content: '';
  display: inline-block;
  height: 8px;
  left: 0;
  position: absolute;
  top: 0;
  width: 8px;
}
.bn-arrow.bn-next {
  -moz-transform: rotate(315deg);
  -ms-transform: rotate(315deg);
  -webkit-transform: rotate(315deg);
  transform: rotate(315deg);
  left:-3px;
}
.bn-arrow.bn-prev {
  -moz-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  -webkit-transform: rotate(135deg);
  transform: rotate(135deg);
  left: 3px;
}
.bn-arrow::after{
  border-color: #999999;
}
.bn-arrow::before{
  background-color: #999999;
}
.bn-play{
  position: relative;
  background: #999999;
  width: 0;
  height: 12px;
  display: inline-block;
  margin-left: -5px;
}
.bn-play::after {
  left: 100%;
  top: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(136, 183, 213, 0);
  border-left-color: #999999;
  border-width: 8px;
  margin-top: -8px;
}
.bn-pause{
  position: relative;
  width: 0;
  height: 14px;
  display: inline-block;
  top: 1px;
  left:-1px;
}
.bn-pause::before{
  position: absolute;
  content: "";
  width: 2px;
  height: 100%;
  left: 3px;
  background-color: #999999;
}
.bn-pause::after{
  position: absolute;
  content: "";
  width: 2px;
  height: 100%;
  left: -3px;
  background-color: #999999;
}
/*controls style end *************/
/*********************************/
.bn-direction-rtl{
  direction: rtl;
}
.bn-direction-rtl .bn-label{
  left: auto;
  right: 0;
}
.bn-direction-rtl .bn-controls{
  right: auto;
  left: 0;
}
.bn-direction-rtl .bn-seperator{
  margin-left: 15px;
  margin-right: 0;
  float: right;
}
.bn-direction-rtl .bn-prefix{
  margin-left: 15px;
  margin-right: 0;
  padding-left: 0;
  padding-right: 10px;
}
.bn-direction-rtl .bn-controls button{
  border-left: none;
  border-right: solid 1px #EEE;
}

.bn-effect-scroll .bn-news ul{
  display: block;
  width: 100%;
  position: relative;
}
.bn-effect-scroll .bn-news ul li{
  display: list-item;
  float: left;
  position: relative;
  width: auto;
}
.bn-effect-scroll.bn-direction-rtl .bn-news ul li{
  float: right;
}

/*********************************/
/*********************************/
