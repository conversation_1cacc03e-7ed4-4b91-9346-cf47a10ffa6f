<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Obtain extends Model
{
    protected $table = 'replaceemail';
    protected $primaryKey = 'ID';
    const CREATED_AT = 'Date';
    const UPDATED_AT = 'updated_at';
    protected $fillable = [
    	'Ministry',
    	'NameDepAr',
    	'NameDepEn',
    	'NameDirectorDepartment',
    	'PhoneNumber',
    	'UserName',
    	'UserNamePhone',
    	'StatusEmail',
    	'Email',
    	'File',
    	'IP',
    	'BrowserAndReports'
    ];
}
