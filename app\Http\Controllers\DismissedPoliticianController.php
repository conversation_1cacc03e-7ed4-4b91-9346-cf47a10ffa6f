<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\DismissedPolitician;

class DismissedPoliticianController extends Controller
{
    public function index() {
        return view('form.dismissed-politicians.dismissed-politicians');
    }
    public function store(Request $request) {

        $this->validate($request, [
            'photo'                         => 'required|image|mimes:jpg,png,jpeg,gif,svg|max:500',
            'firstname'                     => 'required',
            'secondname'                    => 'required',
            'thirdname'                     => 'required',
            'fourthname'                    => 'required',
            'familyname'                    => 'required',
            'mothername'                    => 'required',
            'g-recaptcha-response'          => 'required|captcha',
        ], [
            'photo.required'                         => 'الصورة مطلوب',
            'photo.max'                              => 'الصورة بلغت الحد المسموح يرجى رفع صورة اقل من 500 كيلو بايت',
            'photo.mimes'                            => 'يرجى اختيار المتدادات (jpg,png,jpeg,gif,svg)',
            'photo.image'                            => 'الامتداد غير معروف يرجى اختيار صورة',
            'firstname.required'                     => 'الحقل مطلوب',
            'secondname.required'                    => 'الحقل مطلوب',
            'thirdname.required'                     => 'الحقل مطلوب',
            'fourthname.required'                    => 'الحقل مطلوب',
            'familyname.required'                    => 'الحقل مطلوب',
            'mothername.required'                    => 'الحقل مطلوب',
            'g-recaptcha-response.required' => 'التحقق مطلوب.',
            'g-recaptcha-response.captcha' => 'خطأ في التحقق.',
        ]);
        
        DismissedPolitician::create([
            'photo'                         => $request->photo->store('/uploads/forminfo1', 'public'),
            'fullname'                      => $request->firstname . ' ' . $request->secondname . ' ' . $request->thirdname . ' ' . $request->fourthname . ' ' . $request->familyname,
            'birthday'                      => $request->birthday,
            'mothername'                    => $request->mothername,
            'mobile1'                       => $request->mobile1,
            'mobile2'                       => $request->mobile2,
            'idnumber'                      => $request->idnumber,
            'idissuer'                      => $request->idissuer,
            'iddate'                        => $request->iddate,
            'previousgov'                   => $request->previousgov,
            'previouseliminate'             => $request->previouseliminate,
            'previousside'                  => $request->previousside,
            'previoussection'               => $request->previoussection,
            'previousnumhome'               => $request->previousnumhome,
            'previousclosestpoint'          => $request->previousclosestpoint,
            'currentgov'                    => $request->currentgov,
            'currenteliminate'              => $request->currenteliminate,
            'currentside'                   => $request->currentside,
            'currentsection'                => $request->currentsection,
            'currentnumhome'                => $request->currentnumhome,
            'currentclosestpoint'           => $request->currentclosestpoint,
        ]);
        return redirect()->back()->with('successsendforminfo1', 'تم ارسال الطلب بنجاح!');
    }
}
