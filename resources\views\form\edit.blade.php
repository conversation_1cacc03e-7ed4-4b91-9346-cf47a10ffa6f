<!DOCTYPE html>
<html dir="rtl">
<head>
	<meta charset="utf-8">
	<meta name="twitter:widgets:autoload" content="off">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="csrf-token" content="{{ csrf_token() }}">
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta property="og:url" content="{{ url('') }}" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<title>استمارة اصدار هوية موظفي وباج دخول ديوان محافظة كركوك</title>
	<!-- Css Files -->
	<link rel="shortcut icon" href="{{ asset('public/img/favicon.ico') }}">
	<link rel="stylesheet" type="text/css" href="{{ asset('public/css/datepicker.min.css') }}">
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
	<link rel="stylesheet" type="text/css" href="{{ asset('public/css/all.min.css') }}">
	<link rel="stylesheet" type="text/css" href="{{ asset('public/css/form.css') }}">
</head>
<body>
	<div class="container">
		<div class="custom-form">
			<h4 class="text-center">استمارة اصدار هوية موظفي وباج دخول ديوان محافظة كركوك</h4>
			<hr width="100%" style="margin: auto; padding-bottom: 20px; padding-top: 10px">

            @if (session()->has('successsendidenf'))
                <div class="alert alert-success text-center">
                    {!! session('successsendidenf') !!}
                </div>
            @else
            @if ($errors->any())
                <div class="alert alert-danger text-right" id="dispalyErrorCustom" style="">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
			<form action="{{ route('form.update', $getEdit->id) }}" style="position: relative;" method="POST" enctype="multipart/form-data" >
				@csrf
				<div class="form-group row text-right" >
					<label class="col-sm-3 col-form-label" >الحالة</label>
					<div class="col-sm-4">
						<input type="text" class="form-control" readonly value="{{ $getEdit->f1 }}" name="f1">
					</div>
				</div>
				<div class="text-right">
				<div class="file-upload" style="position: absolute; left: 0;top: -20px;">
				  <div class="image-upload-wrap" style="display: none;">
				  	<input type="hidden"  value="{{ $getEdit->f2 }}" name="oldf2">
				    <input name="f2" class="file-upload-input @error('f2') is-invalid @enderror" type='file' onchange="readURL(this);" accept="image/*" />
				    <div class="drag-text">
				      <h3>اسحب او اضغط لرفع صورة شخصية</h3>
				      <small class="text-danger">
				      	يرجى رفع صورة بصيغه PNG بدون خلفية وحجم كحد اقصى 500 كيلو بايت
				      </small>
				    </div>
				  </div>
				  <div class="file-upload-content" style="display: block;">
				    <img class="file-upload-image" src="{{ url('public/storage/' . $getEdit->f2) }}" alt="your image" />
				    <div class="image-title-wrap">
				      <button type="button" onclick="removeUpload()" class="remove-image">حذف <span class="image-title">الصورة </span></button>
				    </div>
				  </div>				  
				</div>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">الاسم الثلاثي</label>
				    <div class="col-sm-4">
				    	<input type="text" readonly value="{{ $getEdit->f3 }}" class="form-control">
				    </div>
				  </div>
				  @if ($getEdit->f1 != 'تنسيب' )
				  <div class="form-group row" >
				    <label class="col-sm-3 col-form-label">الاسم باللغة الانكليزية</label>
				    <div class="col-sm-4">
				    	<input type="text" name="f4" value="{{ $getEdit->f4 }}" class="form-control">
				    </div>
				  </div>
				  @endif
				  @if ($getEdit->f1 == 'اخرى' || $getEdit->f1 == 'باج دخول ديوان محافظة كركوك')
				  	@if (!empty($getEdit->ff6))
	  			  		<div class="form-group row" id="hiddenjobs" style="">
	  			  			<label class="col-sm-3 col-form-label">موظف</label>
	    				    <div class="col-sm-4">
	    				    	<input type="text" id="requirejobs" readonly value="{{ $getEdit->ff6 }}" class="form-control">
	    				    </div>
	    			   </div>
	    			@endif
	    			   @if ($getEdit->ff6 == 'نعم')
	    			   <div class="form-group row" id="hiddenjobs1" style="">
	    			   		<label class="col-sm-3 col-form-label">العنوان الوظيفي</label>
	    			   		<div class="col-sm-4">
	    			   			<input type="text" id="requirejobs" name="ff7" value="{{ $getEdit->ff7 }}" class="form-control">
	    			   		</div>
	    			   	</div>
	    			   	<div class="form-group row" id="hiddenjobs2" style="">
	    			   		<label class="col-sm-3 col-form-label">اسم الدائرة </label>
	    			   		<div class="col-sm-4">
	    			   			<input type="text" id="requirejobs1" name="ff8" value="{{ $getEdit->ff8 }}" class="form-control">
	    			   		</div>
	    			   </div>
	    			   @elseif ($getEdit->ff6 == 'لا')
	    			   <div class="form-group row" id="hiddenjobs3" style="">
	    			   	<label class="col-sm-3 col-form-label">المهنة</label>
	    			   	<div class="col-sm-4">
	    			   		<input type="text" id="requirejobs2" name="ff5" value="{{ $getEdit->ff5 }}" class="form-control">
	    			   	</div>
	    			   </div>

	    			   <div class="form-group row" id="hiddenjobs4" style="">
	    			   	<label class="col-sm-3 col-form-label">مكان العمل</label>
	    			   	<div class="col-sm-4">
	    			   		<input type="text" id="requirejobs3" name="ff9" value="{{ $getEdit->ff9 }}" class="form-control">
	    			   	</div>
	    			   </div>
	    			   @endif
				  <div class="form-group row" >
					<label class="col-sm-3 col-form-label">سبب طلب باج الدخول</label>
				  <div class="col-sm-4">
					  <textarea name="ff4" class="form-control" rows="3">{{ $getEdit->ff4 }}</textarea>
				  </div>
				</div>
				  @else
				  <div class="form-group row" >
				  	@if ($getEdit->f1 == 'تنسيب')
				  	<label class="col-sm-3 col-form-label">اسم الدائرة المنسب منها</label>
				  	@else
				    <label class="col-sm-3 col-form-label">العنوان الوظيفي</label>
				    @endif
				    <div class="col-sm-4">
				    	<input type="text" name="f5" value="{{ $getEdit->f5 }}" class="form-control">
				    </div>
				  </div>
				  <div class="form-group row">
						<label class="col-sm-3 col-form-label">الرقم الوظيفي</label>
						<div class="col-sm-4">
							<input type="text" name="job_number" required value="{{ $getEdit->job_number }}" class="form-control">
						</div>
				  </div>
				  <div class="form-group row" id="hiddenallcat" >
				  	@if ($getEdit->f1 == 'تنسيب')
				  	<label class="col-sm-3 col-form-label">القسم او الشعبة المنسب اليها</label>
				  	@else
				    <label class="col-sm-3 col-form-label">القسم او الشعبة</label>
				    @endif
				    <div class="col-sm-4">
				    	<input type="text" name="f6" id="sectionJobs" value="{{ $getEdit->f6 }}" class="form-control">
				    </div>
				  </div>
				  @endif
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">هل تملك هوية او باج  دخول سابقاً </label>
				    <div class="col-sm-4">
				    	<select class="form-control @error('f7') is-invalid @enderror" name="f7" onchange="checkifexist(this);">
				    		<option value="نعم" @if($getEdit->f7 == 'نعم') selected @endif >نعم</option>
				    		<option value="لا" @if($getEdit->f7 == 'لا') selected @endif>لا</option>
						</select>
						@error('f7')
		                    <span class="invalid-feedback" role="alert">
		                        <strong>{{ $message }}</strong>
		                    </span>
		                @enderror
				    </div>
				  </div>
				  <div>
				  	@if ($getEdit->f7 == 'نعم')
				    <div class="form-group row" id="customshow">
			    	@else
			    	<div class="form-group row" id="customshow" style="display: none;">
			    	@endif
				    	<label class="col-sm-3 col-form-label">رقم الهوية</label>
				    	<div class="col-sm-4">
				    		<input type="text" name="f8" id="idNumber" value="{{ $getEdit->f8 }}" class="form-control">
				    	</div>
				    	<div class="input-group mb-3 col-sm-6">
						  <div class="custom-file ">
						  	<label class="custom-file-label text-center" for="idnumberupload">رفع نسخة من  الهوية السابقة </label>
						  	<input type="hidden" name="oldf9" value="{{ $getEdit->f9 }}">
						    <input type="file" name="f9" accept="image/*"  class="custom-file-input @error('f9') is-invalid @enderror" id="idnumberupload" aria-describedby="inputGroupFileAddon01">
		    				@error('f9')
		                        <span class="invalid-feedback" role="alert">
		                            <strong>{{ $message }}</strong>
		                        </span>
		                    @enderror
						  </div>
				    	</div>
				    </div>
				  </div>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">رقم الهاتف</label>
				    <div class="col-sm-4">
				    	<input type="number" readonly value="{{ $getEdit->f10 }}" class="form-control">
				    </div>
				  </div>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">البريد الألكتروني ان وجد</label>
				    <div class="col-sm-4">
				    	<input type="email" name="f11" value="{{  $getEdit->f11 }}" class="form-control">
				    </div>
				  </div>
				  @if ($getEdit->f1 != 'اخرى')
				  <div class="form-group row">
				  	@if ($getEdit->f1 == 'تنسيب')
				  	<label class="col-sm-3 col-form-label" >تاريخ التنسيب</label>
				  	@else
				    <label class="col-sm-3 col-form-label" >تاريخ التعيين</label>
				    @endif
				    <div class="col-sm-4">
				    	<input type="text" data-toggle="datepicker" readonly name="f12" value="{{ $getEdit->f12 }}" class="form-control requireDate">
				    </div>
				  </div>
				  @endif
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">عنوان السكن</label>
				    <div class="col-sm-3">
				    	<input type="text" name="f13" value="{{ $getEdit->f13 }}" class="form-control @error('f13') is-invalid @enderror" placeholder="المحافظة">
    					@error('f13')
    	                    <span class="invalid-feedback" role="alert">
    	                        <strong>{{ $message }}</strong>
    	                    </span>
    	                @enderror
				    </div>
				    <div class="col-sm-3">
				    	<input type="text" name="f14" value="{{ $getEdit->f14 }}" class="form-control @error('f14') is-invalid @enderror" placeholder="القضاء">
    					@error('f14')
    	                    <span class="invalid-feedback" role="alert">
    	                        <strong>{{ $message }}</strong>
    	                    </span>
    	                @enderror
				    </div>
				    <div class="col-sm-3">
				    	<input type="text" name="f15" value="{{ $getEdit->f15 }}" class="form-control @error('f15') is-invalid @enderror" placeholder="الناحيه">
    					@error('f15')
    	                    <span class="invalid-feedback" role="alert">
    	                        <strong>{{ $message }}</strong>
    	                    </span>
    	                @enderror
				    </div>
				  </div>
				  <div class="form-group row">
				    <div class="col-sm-3">
				    	<input type="text" name="f16" value="{{ $getEdit->f16 }}" class="form-control" placeholder="محلة">
				    </div>
				    <div class="col-sm-3">
				    	<input type="text" name="f17" value="{{ $getEdit->f17 }}" class="form-control" placeholder="زقاق">
				    </div>
				    <div class="col-sm-3">
				    	<input type="text" name="f18" value="{{ $getEdit->f18 }}" class="form-control" placeholder="دار">
				    </div>
				  </div>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">اقرب نقطة دالة</label>
				    <div class="col-sm-3">
				    	<input type="text" name="f19" value="{{ $getEdit->f19 }}" class="form-control @error('f19') is-invalid @enderror">
    					@error('f19')
    	                    <span class="invalid-feedback" role="alert">
    	                        <strong>{{ $message }}</strong>
    	                    </span>
    	                @enderror
				    </div>
				  </div>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">هل تملك مركبة خاصة</label>
				    @if($getEdit->f20 == 'لا')
				    <div class="col-sm-6" id="hiddenNote">
				    @else
				    <div class="col-sm-6" id="hiddenNote" style="display: none;">
				    @endif
				    	<small class="text-danger">ملاحظة: هذا الحقل يملأ فقط في حالة كنت من احد الاقسام المشموله بدخول مركبتكم في مراَب ديوان المحافظة او كنت تملك تصريح دخول</small>
				    </div>
				    <div class="col-sm-3">
				    	<select class="form-control" id="requireCustom55" name="f20" onchange="yesnoCheck2(this);">
				    		<option value="نعم" @if($getEdit->f20 == 'نعم') selected @endif>نعم</option>
				    		<option value="لا" @if($getEdit->f20 == 'لا') selected @endif>لا</option>
				    	</select>
				    </div>
				    @if($getEdit->f20 == 'نعم')
				    <div class="col-sm-6" id="customfile">
				    @else
				    <div class="col-sm-6" id="customfile" style="display: none;">
				    @endif
					  <div class="custom-file">
					  	<label class="custom-file-label text-center" for="inputGroupFile01">رفع نسخة من سنوية المركبة</label>
					  	<input type="hidden" name="oldf21" value="{{ $getEdit->f21 }}">
					    <input type="file" accept="image/*" name="f21"  class="custom-file-input @error('f21') is-invalid @enderror" id="inputGroupFile01" id="haveCar" aria-describedby="inputGroupFileAddon01">
		    				@error('f21')
		                        <span class="invalid-feedback" role="alert">
		                            <strong>{{ $message }}</strong>
		                        </span>
		                    @enderror
					  </div>
					</div>
				  </div>
				  	@if($getEdit->f20 == 'نعم')
				  	<div class="form-group row" id="ifYes2">
				  	@else
					<div class="form-group row" id="ifYes2" style="display: none;">
					@endif
						<div class="col-sm-4">
							<input type="text" name="f22" id="haveCar1" value="{{ $getEdit->f22 }}" class="form-control" placeholder="اسم صاحب السنوية">
						</div>
						<div class="col-sm-2">
							<input type="text" name="f23" id="haveCar2" value="{{ $getEdit->f23 }}" class="form-control" placeholder="رقمها">
						</div>
						<div class="col-sm-2">
						    <select class="form-control" name="f24" id="haveCar3" >
						    	<option value="حكومي"  @if($getEdit->f20 == 'حكومي') selected @endif>حكومي</option>
								<option value="منفيس"  @if($getEdit->f20 == 'منفيس') selected @endif>منفيس</option>
								<option value="كركوك"  @if($getEdit->f20 == 'كركوك') selected @endif>كركوك</option>
								<option value="بغداد" @if($getEdit->f20 == 'بغداد') selected @endif>بغداد</option>
								<option value="البصرة" @if($getEdit->f20 == 'البصرة') selected @endif>البصرة</option>
								<option value="ميسان" @if($getEdit->f20 == 'ميسان') selected @endif>ميسان</option>
								<option value="ذي قار" @if($getEdit->f20 == 'ذي قار') selected @endif>ذي قار</option>
								<option value="الديوانية" @if($getEdit->f20 == 'الديوانية') selected @endif>الديوانية</option>
								<option value="المثنى" @if($getEdit->f20 == 'المثنى') selected @endif>المثنى</option>
								<option value="النجف الاشرف" @if($getEdit->f20 == 'النجف الاشرف') selected @endif>النجف الاشرف</option>
								<option value="كربلاء المقدسة" @if($getEdit->f20 == 'كربلاء المقدسة') selected @endif>كربلاء المقدسة</option>
								<option value="بابل" @if($getEdit->f20 == 'بابل') selected @endif>بابل</option>
								<option value="واسط" @if($getEdit->f20 == 'واسط') selected @endif>واسط</option>
								<option value="ديالى" @if($getEdit->f20 == 'ديالى') selected @endif>ديالى</option>
								<option value="صلاح الدين" @if($getEdit->f20 == 'صلاح الدين') selected @endif>صلاح الدين</option>
								<option value="نينوى" @if($getEdit->f20 == 'نينوى') selected @endif>نينوى</option>
								<option value="الانبار" @if($getEdit->f20 == 'الانبار') selected @endif>الانبار</option>
								<option value="اربيل" @if($getEdit->f20 == 'اربيل') selected @endif>اربيل</option>
								<option value="دهوك" @if($getEdit->f20 == 'دهوك') selected @endif>دهوك</option>
								<option value="سليمانية" @if($getEdit->f20 == 'سليمانية') selected @endif>سليمانية</option>

							</select>
						</div>
						<div class="col-sm-2">
							<input type="text" name="f25" id="haveCar4" value="{{ $getEdit->f25 }}" class="form-control" placeholder="نوعها">
						</div>
						<div class="col-sm-2">
							<input type="text" name="f26" id="haveCar5" value="{{ $getEdit->f26 }}" class="form-control" placeholder="موديلها">
						</div>
					</div>
				  @if($getEdit->f1 == 'اخرى')
					<div class="form-group row" id="hiddenwepon" >
				    <label class="col-sm-3 col-form-label">هل تملك سلاح</label>
				    @if($getEdit->f27 == 'نعم')
				    <div class="col-sm-6" id="hiddenNote2" style="display: none;">
				    @else
				    <div class="col-sm-6" id="hiddenNote2">
				    @endif
				    	<small class="text-danger">ملاحظة: هذا الحقل يملأ فقط في حالة كنت تملك تخويل حمل السلاح.</small>
				    </div>
				    <div class="col-sm-3">
				    	<select class="form-control" id="requireCustom66" name="f27" onchange="yesnoCheck3(this);">
				    		<option value="نعم" @if($getEdit->f27 == 'نعم') selected @endif>نعم</option>
				    		<option value="لا" @if($getEdit->f27 == 'لا') selected @endif>لا</option>
				    	</select>
				    </div>
				    @if($getEdit->f27 == 'نعم')
				    <div class="col-sm-6" id="customfile2">
				    @else
				    <div class="col-sm-6" id="customfile2" style="display: none;">
				    @endif
					  <div class="custom-file">
					  	<label class="custom-file-label text-center" for="inputGroupFile01">رفع نسخة من رخصة السلاح</label>
					  	<input type="hidden" name="oldf28" value="{{ $getEdit->f28 }}">
					    <input type="file" name="f28" accept="image/*" class="custom-file-input @error('f28') is-invalid @enderror" id="inputGroupFile01" id="havewepone" aria-describedby="inputGroupFileAddon01">
		    				@error('f28')
		                        <span class="invalid-feedback" role="alert">
		                            <strong>{{ $message }}</strong>
		                        </span>
		                    @enderror
					  </div>
					</div>
				  </div>
					  @if($getEdit->f27 == 'نعم')
					  <div class="form-group row" id="ifYes3">
					  @else
					  <div class="form-group row" id="ifYes3" style="display: none;">
					  @endif
						<div class="col-sm-3">
							<input type="text" name="f29" id="havewepone1" value="{{ $getEdit->f29 }}" class="form-control" placeholder="نوع السلاح">
						</div>
						<div class="col-sm-3">
							<input type="text" name="f30" id="havewepone2" value="{{$getEdit->f30 }}" class="form-control" placeholder="رقم السلاح ">
						</div>
						<div class="col-sm-3">
							<input type="text" name="f31" id="havewepone3" value="{{ $getEdit->f31 }}" class="form-control" placeholder="رقم الرخصة ">
						</div>
						<div class="col-sm-3">
							<input type="text" data-toggle="datepicker" id="havewepone4" readonly name="f32" value="{{ $getEdit->f32 }}" class="form-control" placeholder="التاريخ">
						</div>
					</div>
					@endif
					<hr>
					<div class="text-right" style="display: block;">
						<p><span class="text-danger">ملاحظة</span>: الفئات المشموله بدخول المركبات الى مراَب ديوان محافظة كركوك بدون تصريح دخول هي :-</p>
						<ol>
							<li>موظفي مكتب السيد المحافظ.</li>
							<li>الهيئة الاستشارية.</li>
							<li>معاوني السيد المحافظ.</li>
							<li>مدراء الاقسام.</li>
							<li>معاوني مدراء الاقسام</li>
							<li>من يملكون تصريح من مدير الموارد البشرية</li>
						</ol>
						<p>على السادة الموظفين الذين يملكون تصريح دخول مركبة الى مراَب ديوان محافظة كركوك خلاف ماذكر اعلاه يرجى <label for="uploadFile" style="color: blue; cursor: pointer;">رفعه هنا</label>  </p>
						<input type="hidden" name="oldf33" value="<?php echo $getEdit->f33 ?>">
						<input type="file" name="f33" id="uploadFile"  class="@error('f33') is-invalid @enderror" onchange="showMyImage(this)" style="display: none;" >
	    				@error('f33')
	                        <span class="invalid-feedback" role="alert">
	                            <strong>{{ $message }}</strong>
	                        </span>
	                    @enderror
						<div class="text-center">
							@if (!empty($getEdit->f33))
							<img id="thumbnil" src="{{ url('public/storage/' . $getEdit->f33) }}" style="margin-top: 5px; margin-bottom: 5px;" width='150'><br/>
							<button type="button" class="btn btn-danger btn-sm" id="showbotton" onclick="document.getElementById('thumbnil').style.display='none';this.style.display='none';" >حذف <span class="image-title2">الصورة </span></button>
							@endif
							
						</div>
					</div>
					<hr>
			        <div style="width: 300px; margin: 0 auto!important">
			          <div class="form-group">
			              {!! NoCaptcha::renderJs() !!}
			              {!! NoCaptcha::display() !!}
			            <span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
			          </div>
			        </div>
			        <div class="text-center">
			        	<input type="submit" value="تحديث" class="btn btn-primary">					
					</div>	
				</div>
				<div id="noyet" style="display: none;">
					<h4 class="text-center text-danger">غير متاح حالياً</h4>
				</div>
			@endif
		</div>
	</div>
	<!-- JavaScript Files -->
    <script src="{{ asset('public/js/jquery-3.4.1.min.js') }}"></script>
    <script src="{{ asset('public/js/popper.min.js') }}"></script>
    <script src="{{ asset('public/js/datepicker.min.js') }}"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
    <script src="{{ asset('public/js/all.min.js') }}"></script>
    <script src="{{ asset('public/js/form.js') }}"></script>
</body>
</html>
