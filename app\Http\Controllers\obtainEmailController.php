<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Obtain;
use App\Instruction;
use App\Http\Requests\ObtainRequest;
use Illuminate\Support\Str;

class obtainEmailController extends Controller
{
	public function index()
	{
		$obtain = Instruction::first()->Instructions;
		return view('form.obtain-email.obtain-email')->with('obtain', $obtain);
	}
	public function store(ObtainRequest $request)
	{
		$u_agent = \Request::userAgent();
		$bname = 'Unknown';
		$platform = 'Unknown';
		$version = "";

		//First get the platform?
		if (preg_match('/linux/i', $u_agent)) {
			$platform = 'linux';
		} elseif (preg_match('/macintosh|mac os x/i', $u_agent)) {
			$platform = 'mac';
		} elseif (preg_match('/windows|win32/i', $u_agent)) {
			$platform = 'windows';
		}

		// Next get the name of the useragent yes seperately and for good reason
		if (preg_match('/MSIE/i', $u_agent) && !preg_match('/Opera/i', $u_agent)) {
			$bname = 'Internet Explorer';
			$ub = "MSIE";
		} elseif (preg_match('/Firefox/i', $u_agent)) {
			$bname = 'Mozilla Firefox';
			$ub = "Firefox";
		} elseif (preg_match('/Chrome/i', $u_agent)) {
			$bname = 'Google Chrome';
			$ub = "Chrome";
		} elseif (preg_match('/Safari/i', $u_agent)) {
			$bname = 'Apple Safari';
			$ub = "Safari";
		} elseif (preg_match('/Opera/i', $u_agent)) {
			$bname = 'Opera';
			$ub = "Opera";
		} elseif (preg_match('/Netscape/i', $u_agent)) {
			$bname = 'Netscape';
			$ub = "Netscape";
		}

		// finally get the correct version number
		$known = array('Version', $ub, 'other');
		$pattern = '#(?<browser>' . join('|', $known) .
			')[/ ]+(?<version>[0-9.|a-zA-Z.]*)#';
		if (!preg_match_all($pattern, $u_agent, $matches)) {
			// we have no matching number just continue
		}

		// see how many we have
		$i = count($matches['browser']);
		if ($i != 1) {
			//we will have two since we are not using 'other' argument yet
			//see if version is before or after the name
			if (strripos($u_agent, "Version") < strripos($u_agent, $ub)) {
				$version = $matches['version'][0];
			} else {
				$version = $matches['version'][1];
			}
		} else {
			$version = $matches['version'][0];
		}

		// check if we have a number
		if ($version == null || $version == "") {
			$version = "?";
		}

		$relativePath = NULL;
		if ($request->hasFile('f10')) {
			$file = $request->file('f10');

			// تحديد المسار
			$uploadPath = public_path('uploads/obtain');

			// إنشاء المجلد إذا لم يكن موجوداً
			if (!file_exists($uploadPath)) {
				mkdir($uploadPath, 0755, true);
			}

			// إنشاء اسم فريد للملف مع الحفاظ على الامتداد الأصلي
			$fileName = Str::random(20) . '.' . $file->getClientOriginalExtension();

			// نقل الملف إلى المسار المطلوب
			$file->move($uploadPath, $fileName);

			// المسار النسبي للملف (لحفظه في قاعدة البيانات مثلاً)
			$relativePath = 'uploads/obtain/' . $fileName;
		}
		// Request Form
		Obtain::create([
			'Ministry'					=> $request->f1,
			'NameDepAr'					=> $request->f2,
			'NameDepEn'					=> $request->f3,
			'NameDirectorDepartment'	=> $request->f4,
			'PhoneNumber'				=> $request->f5,
			'UserName'					=> $request->f6,
			'UserNamePhone'				=> $request->f7,
			'StatusEmail'				=> $request->f8,
			'Email'						=> $request->f9,
			'File'						=> $relativePath,
			'IP'						=> \Request::ip(),
			'BrowserAndReports'			=> $bname . ' ' . $version . ' ' . $u_agent,
		]);

		$idMatch = Obtain::where('UserName', $request->f6)->first()->ID;
		return redirect()->back()->with('successsendobtain', 'تم استلام طلبكم بنجاح رقم الطلب هو:<br/>' . $idMatch . '<br/>سوف يتم ارسال رسالة نصية الى رقم هاتف مستخدم البريد الألكتروني لغرض المراجعة لتسديد الأجور واستلام البريد.');
	}
}
