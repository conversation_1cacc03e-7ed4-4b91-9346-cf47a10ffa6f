<!doctype html>
<html lang="ar" dir="rtl">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css"
        integrity="sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb" crossorigin="anonymous">

    <title>طباعة</title>
    <style>
        @import url(//fonts.googleapis.com/earlyaccess/notokufiarabic.css);

        body {
            font-family: 'Noto Ku<PERSON> Arabic', sans-serif;

            background-repeat: no-repeat;
            background-size: 100%;
            z-index: -99;
        }

        @media print {
            body {
                background-image: url("{{ asset('public/img/99.webp') }}");
            }
        }

        p,
        .form-control,
        .input-group-text {
            font-size: 12px !important;
            text-align: justify !important;
            border: 0 !important;
        }

        .ms-6 {
            margin-right: 5rem !important;
        }
    </style>
</head>

<body>
    @php
    $status = 'اصدار';
    if ($identity->status == 'تجديد') {
    $status = 'تجديد';
    }

    $valupdate = '';
    foreach (\Illuminate\Support\Facades\DB::table('identities_updates_count')
    ->where('form_id', $identity->id)
    ->where('type', '2')->orderBy('id')->get() as $key => $value) {
    if (!empty($value->created_at)) {
    $valupdate .= ($key > 0 ? ' | ' : '') . date('Y-m-d', strtotime($value->created_at));
    }
    }

    @endphp
    {{-- {{$identity->f3}} --}}
    <div class="mt-5" style="width: 85%; margin:auto">
        <p class="ms-6">الى/ لجنة اصدار هويات مجلس محافظة كركوك</p>
        @if ($identity->f1 == 'هوية مجلس')
        <p class="text-center">م/ طلب {{$status}} هوية موظف ملاك جديدة</p>
        <p>تحية طيبة:-</p>
        <p>إني الموظف/ـة (<b>{{$identity->f3}}</b>) على ملاك مجلس محافظة كركوك تحت العنوان الوظيفي
            (<b>{{$identity->ff7}}</b>) والرقم
            الوظيفي ({{$identity->job_number}})
            واعمل في قسم (<b>{{$identity->f6}}</b>) ارجو التفضل بالموافقة على {{$status}} هوية موظف، واتعهد بصحة
            البيانات
            المدرجة في
            الاستمارة ادناه... مع فائق الشكر والتقدير.</p>
        @endif
        @if ($identity->f1 == 'عضو مجلس')
        <p class="text-center">م/ طلب {{$status}} هوية عضو مجلس محافظة</p>
        <p>تحية طيبة:-</p>
        <p>إني عضو مجلس محافظة كركوك (<b>{{$identity->f3}}</b>) والمنتخب من قبل أبناء محافظة كركوك للفترة الانتخابية
            (2023-2027) يرجى أصدار هوية عضو مجلس محافظة كركوك لحاجتنا الماسة اليها... مع فائق الشكر والتقدير.</p>
        @endif
        @if ($identity->f1 == 'تابع عضو')
        <p class="text-center">م/ طلب {{$status}} هوية تابع عضو مجلس محافظة جديدة</p>
        <p>تحية طيبة:-</p>
        <p>إني (<b>{{$identity->f3}}</b>) تابع لعضو (<b>{{$identity->ffff7}}</b>) بالوصف الوظيفي
            (<b>{{$identity->fff7}}</b>), ارجو
            التفضل بالموافقة على اصدار هوية تابع عضو، واتعهد بصحة البيانات المدرجة في الاستمارة ادناه... مع فائق الشكر
            والتقدير.</p>
        @endif
        <hr>
        @if ($identity->f1 == 'هوية مجلس')
        <div class="row">
            <div class="col-7">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم الاستمارة:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->id}}">
                </div>
            </div>
            <div class="col-5">
                <div class="input-group mb-3">
                    <span class="input-group-text">تاريخ الاستمارة:</span>
                    <input type="text" class="form-control" readonly
                        value="{{ date('Y-m-d', strtotime($identity->created_at)) }}">
                </div>
            </div>
            <div class="col-12">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم الهوية:</span>
                    <input type="text" class="form-control" readonly value="{{$getidnumber}}">
                </div>
            </div>
            <div class="col-7">
                <div class="input-group mb-3">
                    <span class="input-group-text">الاسم باللغة الإنكليزية:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f4}}">
                </div>
            </div>
            <div class="col-5">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم الهاتف:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f10}}">
                </div>
            </div>
            <div class="col-7">
                <div class="input-group mb-3">
                    <span class="input-group-text">الحالة الزوجية:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f1new}}">
                </div>
            </div>
            <div class="col-5">
                <div class="input-group mb-3">
                    <span class="input-group-text">سنة التولد:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f2new}}">
                </div>
            </div>
            <div class="col-7">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم البطاقه الوطنية:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f3new}}">
                </div>
            </div>
            <div class="col-5">
                <div class="input-group mb-3">
                    <span class="input-group-text">التاريخ والاصدار:</span>
                    <input type="text" class="form-control" readonly
                        value="{{$identity->f4new . ' ' . $identity->f5new}}">
                </div>
            </div>
            <div class="col-sm-8">
                <div class="row">
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">رقم جواز السفر:</span>
                            <input type="text" class="form-control" readonly value="{{$identity->f6new}}">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">عنوان السكن:</span>
                            <input type="text" class="form-control" readonly
                                value="{{$identity->f13 . ' ' . $identity->f14 . ' ' . $identity->f15 . ' ' . $identity->f16}}">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">اقرب نقطة دالة:</span>
                            <input type="text" class="form-control" readonly value="{{$identity->f19}}">
                        </div>
                    </div>
                    @if ($identity->f20 == 'نعم')
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">بيانات العجلة:</span>
                            <input type="text" class="form-control" readonly
                                value="{{$identity->f25 . ' ' . $identity->f26 . ' ' . $identity->f23}}">
                        </div>
                    </div>
                    @endif
                    @if ($identity->f27 == 'نعم')
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">بيانات السلاح:</span>
                            <input type="text" class="form-control" readonly
                                value="{{$identity->f29 . ' ' . $identity->f30 . ' ' . $identity->f31}}">
                        </div>
                    </div>
                    @endif
                </div>
                @if (!empty($valupdate))
                <div class="col-12">
                    <div class="input-group mb-3">
                        <span class="input-group-text">التجديد:</span>
                        <input type="text" class="form-control" readonly value="{{$valupdate}}">
                    </div>
                </div>
                @endif
            </div>
            <div class="col-sm-4">
                <img src="{{asset('public/storage/' .$identity->f2)}}" alt="" width="130">
            </div>
        </div>
        @endif
        @if ($identity->f1 == 'عضو مجلس')
        <div class="row">
            <div class="col-7">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم الاستمارة:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->id}}">
                </div>
            </div>
            <div class="col-5">
                <div class="input-group mb-3">
                    <span class="input-group-text">تاريخ الاستمارة:</span>
                    <input type="text" class="form-control" readonly
                        value="{{ date('Y-m-d', strtotime($identity->created_at)) }}">
                </div>
            </div>
            <div class="col-12">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم الهوية:</span>
                    <input type="text" class="form-control" readonly value="{{$getidnumber}}">
                </div>
            </div>
            <div class="col-sm-8">
                <div class="row">
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">الاسم باللغة الإنكليزية:</span>
                            <input type="text" class="form-control" readonly value="{{$identity->f4}}">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">رقم الهاتف:</span>
                            <input type="text" class="form-control" readonly value="{{$identity->f10}}">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <img src="{{asset('public/storage/' .$identity->f2)}}" alt="" width="130">
            </div>
            @if (!empty($valupdate))
            <div class="col-12">
                <div class="input-group mb-3">
                    <span class="input-group-text">التجديد:</span>
                    <input type="text" class="form-control" readonly value="{{$valupdate}}">
                </div>
            </div>
            @endif
        </div>
        @endif
        @if ($identity->f1 == 'تابع عضو')
        <div class="row">
            <div class="col-7">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم الاستمارة:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->id}}">
                </div>
            </div>
            <div class="col-5">
                <div class="input-group mb-3">
                    <span class="input-group-text">تاريخ الاستمارة:</span>
                    <input type="text" class="form-control" readonly
                        value="{{ date('Y-m-d', strtotime($identity->created_at)) }}">
                </div>
            </div>
            <div class="col-12">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم الهوية:</span>
                    <input type="text" class="form-control" readonly value="{{$getidnumber}}">
                </div>
            </div>
            <div class="col-7">
                <div class="input-group mb-3">
                    <span class="input-group-text">الاسم باللغة الإنكليزية:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f4}}">
                </div>
            </div>
            <div class="col-5">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم الهاتف:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f10}}">
                </div>
            </div>
            <div class="col-7">
                <div class="input-group mb-3">
                    <span class="input-group-text">الحالة الزوجية:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f1new}}">
                </div>
            </div>
            <div class="col-5">
                <div class="input-group mb-3">
                    <span class="input-group-text">سنة التولد:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f2new}}">
                </div>
            </div>
            <div class="col-7">
                <div class="input-group mb-3">
                    <span class="input-group-text">رقم البطاقه الوطنية:</span>
                    <input type="text" class="form-control" readonly value="{{$identity->f3new}}">
                </div>
            </div>
            <div class="col-5">
                <div class="input-group mb-3">
                    <span class="input-group-text">تاريخ ومحل الاصدار:</span>
                    <input type="text" class="form-control" readonly
                        value="{{$identity->f4new . ' ' . $identity->f5new}}">
                </div>
            </div>
            <div class="col-sm-8">
                <div class="row">
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">رقم جواز السفر:</span>
                            <input type="text" class="form-control" readonly value="{{$identity->f6new}}">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">عنوان السكن:</span>
                            <input type="text" class="form-control" readonly
                                value="{{$identity->f13 . ' ' . $identity->f14 . ' ' . $identity->f15 . ' ' . $identity->f16}}">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">اقرب نقطة دالة:</span>
                            <input type="text" class="form-control" readonly value="{{$identity->f19}}">
                        </div>
                    </div>
                    @if ($identity->f20 == 'نعم')
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">بيانات العجلة:</span>
                            <input type="text" class="form-control" readonly
                                value="{{$identity->f25 . ' ' . $identity->f26 . ' ' . $identity->f23}}">
                        </div>
                    </div>
                    @endif
                    @if ($identity->f27 == 'نعم')
                    <div class="col-12">
                        <div class="input-group mb-3">
                            <span class="input-group-text">بيانات السلاح:</span>
                            <input type="text" class="form-control" readonly
                                value="{{$identity->f29 . ' ' . $identity->f30 . ' ' . $identity->f31}}">
                        </div>
                    </div>
                    @endif
                </div>
                @if (!empty($valupdate))
                <div class="col-12">
                    <div class="input-group mb-3">
                        <span class="input-group-text">التجديد:</span>
                        <input type="text" class="form-control" readonly value="{{$valupdate}}">
                    </div>
                </div>
                @endif
            </div>
            <div class="col-sm-4">
                <img src="{{asset('public/storage/' .$identity->f2)}}" alt="" width="120">
            </div>
        </div>
        @endif
        <hr style="
    margin: 0px 0 10px 0px!important;">
        <div class="row">
            <div class="col-12 mb-4">
                <p>لجنة اصدار الهويات حسب الامر الإداري المرقم (1850) بتاريخ (09/10/2024)</p>
            </div>
            <div class="col-4">
                <p class="text-center mb-0">عضو</p>
                <p class="text-center mb-0">هاني أحمد سلطان </p>
            </div>
            <div class="col-4">
                <p class="text-center mb-0">عضو</p>
                <p class="text-center mb-0">رؤى جواد كاظم </p>
            </div>
            <div class="col-4">
                <p class="text-center mb-0">رئيس اللجنة</p>
                <p class="text-center mb-0">رئيس مجلس محافظة كركوك</p>
                <p class="text-center mb-0">محمد إبراهيم الحافظ</p>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-12 mb-4">
                <p>اصدار هويات ديوان محافظة كركوك</p>
            </div>
            <div class="col-4">
                <p class="text-center mb-0">مدير مكتب محافظ كركوك</p>
                <p class="text-center mb-0">هژار محمد عبد الجبار</p>
            </div>
        </div>
    </div>

    <button style='position: fixed; left: 0;top: 130px' class='btn btn-info'
        onclick="this.style.display='none';window.print();">طباعة</button>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
    </script>
</body>

</html>