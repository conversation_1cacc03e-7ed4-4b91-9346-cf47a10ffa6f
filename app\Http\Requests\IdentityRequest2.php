<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class IdentityRequest2 extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'f1'      => 'required',
            'f3'      => 'required|unique:identities',
            'f4'      => 'required',
            // 'job_number' => 'unique:identities',
            'f9'      => 'mimes:png,jpg,jpeg|max:512',
            'f10'     => 'required|digits:11|unique:identities',
            'f13'     => 'required',
            'f14'     => 'required',
            'f15'     => 'required',
            'f19'     => 'required',
            'f21'      => 'mimes:png,jpg,jpeg|max:512',
            'f28'      => 'mimes:png,jpg,jpeg|max:512',
            'f33'      => 'mimes:png,jpg,jpeg,gif,pdf|max:512',
            'g-recaptcha-response' => 'required|captcha',
        ];
    }
    public function messages()
    {
        return [
            'f1.required'    => 'يرجى اختيار حالة الاستمارة.',
            'f3.required'    => 'الاسم مطلوب .',
            'f4.required'    => 'الاسم باللغة الانكليزية مطلوب .',
            'f3.unique'      => 'هذا الاسم مسجل لدينا.',
            // 'job_number.unique'      => 'الرقم الوظيفي مستخدم.',
            'f10.required'   => 'رقم الهاتف مطلوب.',
            'f10.digits'     => 'يجب ان يكون رقم الهاتف 11 رقم.',
            'f10.unique'     => 'عذراً، هذه الاستمارة مسجلة سابقاً!.',
            'f13.required'   => 'يرجى كتابة محافظة السكن  .',
            'f14.required'   => 'يرجى كتابة القضاء.',
            'f15.required'   => 'يرجى كتابة الناحية.',
            'f19.required'   => 'يرجى ذكر اقرب نقطة دالة.',
            'f9.max'   => 'حجم الملف يجب ان يكون 500KB كحد اقصى.',
            'f21.max'   => 'حجم الملف يجب ان يكون 500KB كحد اقصى.',
            'f28.max'   => 'حجم الملف يجب ان يكون 500KB كحد اقصى.',
            'f33.max'   => 'حجم الملف يجب ان يكون 500KB كحد اقصى.',
            'g-recaptcha-response.required' => 'التحقق مطلوب.',
            'g-recaptcha-response.captcha' => 'خطأ في التحقق.',
        ];
    }
}
