@extends('layout.main')
@section('title', 'الموقع الرسمي | ديوان محافظة كركوك')
@section('title', 'الموقع الرسمي | ديوان محافظة كركوك')
@section('titlee', 'الموقع الرسمي | ديوان محافظة كركوك')
@section('url', url(''))
@section('site_name', 'الموقع الرسمي | ديوان محافظة كركوك')
@section('content')
<div class="container">
	<div class="row g-5">

		<!-- Blog list Start -->
		<div class="col-lg-8">
			<div class="position-sticky" style="top: 10rem;">
				<div class="camera_wrap camera_azure_skin" id="camera_wrap_1">
					@foreach ($getslider ?? '' as $slider)
					<div data-thumb="{{ url('content/uploads') . '/' . $slider->Image }}"
						data-src="{{ url('content/uploads') . '/' . $slider->Image }}">
						<div class="camera_caption fadeFromBottom font-kufi-regular"><a class="text-white"
								href="news/{{ $slider->NewsID }}">{{ $slider->Title }}</a></div>
					</div>
					@endforeach
				</div><!-- #camera_wrap_1 -->
				<div class="row g-5">
					@foreach ($news as $new)
					<div class="col-md-6" data-wow-delay="0.1s">
						<div class="blog-item bg-light rounded overflow-hidden">
							<div class="blog-img position-relative overflow-hidden">
								@if ($new->Dbvanews == 0)
								<img class="img-fluid" src="{{ url('content/uploads') . '/' . $new->Image }}"
									alt="{{ $new->Title }}">
								@elseif ($new->Dbvanews == 2)
								<iframe src="https://www.facebook.com/plugins/video.php?href={{$new->VideoFacebook}}"
									width="500" height="283"
									style="border: none; overflow: hidden;width: 100%;height: 15vw;" scrolling="no"
									frameborder="0" allowfullscreen="true"
									allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
									allowfullscreen="true"></iframe>
								@elseif ($new->Dbvanews == 1)
								<div class="fix video-gallery">
									<div class="player-small" data-wow-delay="0.1s"
										data-property="{videoURL:'{{$new->Video}}',containment:'self',autoPlay:true, mute:true, startAt:0, opacity:1}">
									</div>
								</div>
								@endif
							</div>
							<div class="p-4">
								<div class="d-flex mb-3">
									@if (!empty($new->Count_View))
									<small><i class="fas fa-eye"></i> {{ $new->Count_View }}</small>
									@else
									<small>عدد القراءات : لاتوجد مشاهدات حالياً</small>
									@endif

									<small><i class="far fa-calendar-alt text-primary me-2"></i> {{ $new->Date
										}}</small>
								</div>
								<h6 class="mb-3 font-kufi-bold"><a href="news/{{ $new->NewsID }}">{{ implode(' ',
										array_slice(explode(' ', strip_tags($new->Title)), 0, 10)) }}...</a></h6>
								<p class="font-kufi-regular fs-custom">{{ implode(' ', array_slice(explode(' ',
									strip_tags($new->Content)), 0, 20)) }}...<a class="text-uppercase"
										href="news/{{ $new->NewsID }}">قراءة المزيد... <i
											class="bi bi-arrow-right"></i></a></p>
							</div>
						</div>
					</div>
					@endforeach
					@foreach ($singlenews as $single)
					<div class="col-lg-12 mb-4" data-wow-delay="0.6s">
						<div class="card mb-1 custom-card" style="max-width: 100%;">
							<div class="row g-0">
								<div class="col-md-4">
									@if ($single->Dbvanews == 0)
									<img src="{{ url('content/uploads') . '/' . $single->Image }}"
										class="img-fluid rounded-start" alt="{{ $single->Title }}">
									@elseif ($single->Dbvanews == 2)
									<iframe
										src="https://www.facebook.com/plugins/video.php?href={{$single->VideoFacebook}}"
										width="500" height="283"
										style="border: none; overflow: hidden;max-width: 100%;height: auto;"
										scrolling="no" frameborder="0" allowfullscreen="true"
										allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
										allowfullscreen="true"></iframe>
									@elseif ($single->Dbvanews == 1)
									<div class="fix video-gallery">
										<div class="player-small" data-wow-delay="0.1s"
											data-property="{videoURL:'{{$single->Video}}',containment:'self',autoPlay:true, mute:true, startAt:0, opacity:1}">
										</div>
									</div>
									@endif

								</div>
								<div class="col-md-8">
									<div class="card-body">
										<h6 class="card-title font-kufi-bold"><a href="news/{{ $single->NewsID }}">{{
												implode(' ', array_slice(explode(' ', $single->Title), 0, 10)) }}...</a>
										</h6>
										<p class="card-text font-kufi-regular fs-custom">{!! implode(' ',
											array_slice(explode(' ', strip_tags($single->Content)), 0, 20)) !!}...<a
												href="news/{{ $single->NewsID }}">قراءة المزيد</a></p>
										<p class="card-text"><small class="text-muted"><i
													class="fas fa-calendar-alt"></i> {{ $single->Date }} | <i
													class="fas fa-eye"></i> {{ $single->Count_View }}</small></p>
									</div>
								</div>
							</div>
						</div>
					</div>
					@endforeach
					@foreach ($newsbig as $newbig)
					<div class="col-lg-5 mb-4 custom-card" data-wow-delay="0.6s">
						@if ($newbig->Dbvanews == 0)
						<img class="img-fluid rounded mb-3" src="{{ url('content/uploads') . '/' . $newbig->Image }}"
							alt="{{ $newbig->Title }}">
						@elseif ($newbig->Dbvanews == 1)
						<div class="fix video-gallery">
							<div class="player-small" data-wow-delay="0.1s"
								data-property="{videoURL:'{{$newbig->Video}}',containment:'self',autoPlay:true, mute:true, startAt:0, opacity:1}">
							</div>
						</div>
						@elseif ($newbig->Dbvanews == 2)
						<iframe src="https://www.facebook.com/plugins/video.php?href={{$newbig->VideoFacebook}}"
							width="500" height="283"
							style="border: none; overflow: hidden;max-width: 100%;height: auto;" scrolling="no"
							frameborder="0" allowfullscreen="true"
							allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
							allowfullscreen="true"></iframe>
						@endif
						<a href="news/{{ $newbig->NewsID }}" class="mt-4 h6 text-dark font-kufi-bold">
							{{ implode(' ', array_slice(explode(' ', $newbig->Title), 0, 15)) }}...</a>
						<p class="mt-4 fs-custom font-kufi-regular" style="text-align: justify;margin-bottom: 27px;">{{
							\Illuminate\Support\Str::limit(strip_tags($newbig->Content),
							150, '...') }}</p>

						<div class="d-flex text-small">
							<span class="text-muted ml-1"><i class="fas fa-eye"></i> {{ $newbig->Count_View }} <i
									class="fas fa-calendar-alt"></i> {{ $newbig->Date }}</span>
						</div>

					</div>
					@endforeach
					<div class="col-lg-7" data-wow-delay="0.9s">
						<ul class="list-unstyled pe-0 me-0 custom-border-li">
							@foreach ($newssm as $newsm)
							<li class="row mb-2 align-items-center">
								<a href="news/{{ $newsm->NewsID }}" class="col-3 font-kufi-bold">
									@if ($newsm->Dbvanews == 0)
									<img src="{{ url('content/uploads') . '/' . $newsm->Image }}"
										alt="{{ $newsm->Title }}" class="rounded img-fluid">
									@elseif ($newsm->Dbvanews == 1)

									<img src="{{ url('content/uploads/newsvideo.webp')}}" alt="{{ $newsm->Title }}"
										class="rounded img-fluid">

									@elseif ($newsm->Dbvanews == 2)
									<img src="{{ url('content/uploads/newsvideo.webp')}}" alt="{{ $newsm->Title }}"
										class="rounded img-fluid">
									@endif
								</a>
								<div class="col-9">
									<a href="news/{{ $newsm->NewsID }}">
										<h6 class="mb-3 fs-custom text-dark font-kufi-regular lh-base "
											style="text-align: justify">{{ \Illuminate\Support\Str::limit($newsm->Title,
											35, '...') }}
										</h6>
									</a>
								</div>
							</li>
							@endforeach
						</ul>
					</div>
					<div class="col-md-12" data-wow-delay="0.6s">
						<div class="filtering" style="direction: ltr;">
							@foreach ($newsslider as $newslider)
							<div class="text-center">
								<a href="news/{{ $newslider->NewsID }}" class="text-decoration-none text-dark">
									@if ($newslider->Dbvanews == 0)
									<img width="95%" height="144"
										src="{{ url('content/uploads') . '/' . $newslider->Image }}" alt="">
									@elseif ($newslider->Dbvanews == 1)
									<img width="95%" height="144" src="{{ url('content/uploads/newsvideo.webp')}}"
										alt="{{ $newslider->Title }}">
									@elseif ($newslider->Dbvanews == 2)
									<img width="95%" height="144" src="{{ url('content/uploads/newsvideo.webp')}}"
										alt="{{ $newslider->Title }}">
									@endif
									<h6 style="font-size: 13px; width:95%" class="font-kufi-regular">{{ implode(' ',
										array_slice(explode(' ', $newslider->Title), 0, 15)) }}</h6>
								</a>
							</div>
							@endforeach
						</div>
					</div>
					@foreach (\DB::table('news')->where('status', 0)->skip(16)->take(2)->orderBy('NewsID',
					'desc')->get() as $new)
					<div class="col-md-6" data-wow-delay="0.1s">
						<div class="blog-item bg-light rounded overflow-hidden">
							<div class="blog-img position-relative overflow-hidden">
								@if ($new->Dbvanews == 0)
								<img src="{{ url('content/uploads') . '/' . $new->Image }}"
									class="img-fluid rounded-start" alt="{{ $new->Title }}">
								@elseif ($new->Dbvanews == 2)
								<iframe src="https://www.facebook.com/plugins/video.php?href={{$new->VideoFacebook}}"
									width="500" height="283"
									style="border: none; overflow: hidden;width: 100%;height: 15vw;" scrolling="no"
									frameborder="0" allowfullscreen="true"
									allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
									allowfullscreen="true"></iframe>
								@elseif ($new->Dbvanews == 1)
								<div class="fix video-gallery">
									<div class="player-small" data-wow-delay="0.1s"
										data-property="{videoURL:'{{$new->Video}}',containment:'self',autoPlay:true, mute:true, startAt:0, opacity:1}">
									</div>
								</div>
								@endif
							</div>
							<div class="p-4">
								<div class="d-flex mb-3">
									@if (!empty($new->Count_View))
									<small><i class="fas fa-eye"></i> {{ $new->Count_View }}</small>
									@else
									<small>عدد القراءات : لاتوجد مشاهدات حالياً</small>
									@endif

									<small><i class="far fa-calendar-alt text-primary me-2"></i> {{ $new->Date
										}}</small>
								</div>
								<h6 class="mb-3 font-kufi-bold"><a href="news/{{ $new->NewsID }}">{{ implode(' ',
										array_slice(explode(' ', strip_tags($new->Title)), 0, 10)) }}...</a></h6>
								<p class="font-kufi-regular fs-custom">{{ implode(' ', array_slice(explode(' ',
									strip_tags($new->Content)), 0, 20)) }}...<a class="text-uppercase"
										href="news/{{ $new->NewsID }}">قراءة المزيد... <i
											class="bi bi-arrow-right"></i></a></p>
							</div>
						</div>
					</div>
					@endforeach
					@foreach (\DB::table('news')->where('status', 0)->skip(18)->take(1)->orderBy('NewsID',
					'desc')->get() as $single)
					<div class="col-lg-12 mb-4" data-wow-delay="0.6s">
						<div class="card mb-1 custom-card" style="max-width: 100%;">
							<div class="row g-0">
								<div class="col-md-4">
									@if ($single->Dbvanews == 0)
									<img src="{{ url('content/uploads') . '/' . $single->Image }}"
										class="img-fluid rounded-start" alt="{{ $single->Title }}">
									@elseif ($single->Dbvanews == 2)
									<iframe
										src="https://www.facebook.com/plugins/video.php?href={{$single->VideoFacebook}}"
										width="500" height="283"
										style="border: none; overflow: hidden;max-width: 100%;height: auto;"
										scrolling="no" frameborder="0" allowfullscreen="true"
										allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
										allowfullscreen="true"></iframe>
									@elseif ($single->Dbvanews == 1)
									<div class="fix video-gallery">
										<div class="player-small" data-wow-delay="0.1s"
											data-property="{videoURL:'{{$single->Video}}',containment:'self',autoPlay:true, mute:true, startAt:0, opacity:1}">
										</div>
									</div>
									@endif

								</div>
								<div class="col-md-8">
									<div class="card-body">
										<h6 class="card-title font-kufi-bold"><a href="news/{{ $single->NewsID }}">{{
												implode(' ', array_slice(explode(' ', $single->Title), 0, 10)) }}...</a>
										</h6>
										<p class="card-text font-kufi-regular fs-custom">{!! implode(' ',
											array_slice(explode(' ', strip_tags($single->Content)), 0, 20)) !!}...<a
												href="news/{{ $single->NewsID }}">قراءة المزيد</a></p>
										<p class="card-text"><small class="text-muted"><i
													class="fas fa-calendar-alt"></i> {{ $single->Date }} | <i
													class="fas fa-eye"></i> {{ $single->Count_View }}</small></p>
									</div>
								</div>
							</div>
						</div>
					</div>
					@endforeach
				</div>
			</div>
		</div>
		<!-- Blog list End -->

		@include('sidebar')
	</div>
</div>
@endsection