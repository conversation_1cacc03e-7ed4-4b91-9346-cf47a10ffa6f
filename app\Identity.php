<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Identity extends Model
{
    protected $table = 'identities';
    protected $primaryKey = 'id';
    protected $fillable = [
        'f1',
        'f2',
        'f3',
        'f4',
        'f5',
        'ff4',
        'ff5',
        'ff6',
        'ff7',
        'ff8',
        'ff9',
        'f6',
        'f7',
        'f1new',
        'f2new',
        'f3new',
        'f4new',
        'f5new',
        'f6new',
        'f8',
        'f9',
        'f10',
        'f11',
        'f12',
        'f13',
        'f14',
        'f15',
        'f16',
        'f17',
        'f18',
        'f19',
        'f20',
        'f21',
        'f22',
        'f23',
        'f24',
        'f25',
        'f26',
        'f27',
        'f28',
        'f29',
        'f30',
        'f31',
        'f32',
        'f33',
        'job_number',
        'updateid',
        'status',
        'howmuchupdate',
        'IP',
        'BrowserAndReports',
        'update_identity',
    ];
    public function idnumber()
    {
        return $this->hasOne('App\Idnumber', 'identities_id');
    }
}
