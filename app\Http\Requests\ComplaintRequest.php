<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ComplaintRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'subject'     => 'required|max:255',
            'phone'     => 'required|max:11|min:11',
            'company'     => 'required',
            'msg'         => 'required',
            'file1'       => 'mimes:jpg,jpeg,bmp,png,pdf',
            'file2'       => 'mimes:jpg,jpeg,bmp,png,pdf',
        ];
    }
    public function messages()
    {
        return [
            'subject.required' => 'يرجى كتابه موضوع للشكوى.',
            'subject.max' => 'موضوع الشكوى لايمكن ان يكون اكثر من 255 حرف.',

            'phone.required' => 'رقم الهاتف مطلوب.',
            'phone.max' => 'يجب ان يكون رقم الهاتف 11 رقم.',
            'phone.min' => 'يجب ان يكون رقم الهاتف 11 رقم',

            'company.required' => 'اسم الشركة او اسم بطاقة الماستر كارد مطلوب.',

            'msg.required' => 'نص الشكوى مطلوب.',
            'file1.mimes' => 'امتداد الملف غير مدعوم.',
            'file2.mimes' => 'امتداد الملف غير مدعوم.',
        ];
    }
}
