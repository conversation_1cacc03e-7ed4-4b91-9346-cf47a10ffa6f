@if (\DB::table('status')->where('id', 1)->value('jobsstatus') == 0)

<!doctype html>
<html lang="ar" dir="rtl">

<head>
	<!-- Required meta tags -->
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="csrf-token" content="{{ csrf_token() }}">
	<link rel="shortcut icon" href="{{ asset('public/img/logo.webp') }}">
	<meta property="og:image" content="{{ asset('public/img/logo.webp') }}" />
	<!-- Bootstrap CSS -->
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css"
		integrity="sha384-+qdLaIRZfNu4cVPK/PxJJEy0B0f3Ugv8i482AKY7gwXwhaCroABd086ybrVKTa0q" crossorigin="anonymous">
	<link rel="stylesheet" href="{{ asset('public/jobs/css/main.css') }}">
	<title>استمارة تعاقد 1000 درجة وظيفية</title>
</head>

<body>

	<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel">
		<div class="offcanvas-header">
			<h5 id="offcanvasRightLabel">مثال على تحميل الوثائق</h5>
			<button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
		</div>
		<div class="offcanvas-body">
			<img src="{{ asset('public/jobs/images/1.webp') }}" class="img-fluid">
			<img src="{{ asset('public/jobs/images/2.webp') }}" class="img-fluid">
		</div>
	</div>
	<div id="custom-mobile" class="container">
		<hr>
		<div class="content2">
			<div class="row">
				<div class="col-sm-6 d-flex align-items-center justify-content-center">
					<section class="text-center">
						<h4>ديوان محافظة كركوك</h4>
						<h5>البوابة الالكتروني/ قسم تكنولوجيا المعلومات</h5>
						<h6>استمارة التعاقد على 1000 درجة بصفة عقد لغرض التدريب والتطوير</h6>
					</section>
				</div>
				<div class="col-sm-6 text-end">
					<img src="https://kirkuk.gov.iq/public/img/logo-2.png" width="170">
				</div>
			</div>
		</div>
		<hr>
		@if (session()->has('success'))
		<div class="customprint text-center ml-4 w-75 m-auto">
			<div class="alert alert-success" id="hidealert">
				تم تقديم طلبكم بنجاح
			</div>
			<p><u><em>ملاحظة</em></u></p>
			<p>يرجى الاحتفاظ على هذه الوثيقة لغرض المراجعة</p>
			<table class="table table-bordered">
				<tbody>
					<tr>
						<th>اسم الدائرة الذي تم التقديم عليها</th>
						<td>{!! session('department') !!}</td>
					</tr>
					<tr>
						<th>رقم الطلب</th>
						<td>{!! session('formid') !!}</td>
					</tr>
					<tr>
						<th>تاريخ الطلب</th>
						<td>{!! session('formdate') !!}</td>
					</tr>
					<tr>
						<th>اسم صاحب الطلب</th>
						<td>{!! session('fullname') !!}</td>
					</tr>
				</tbody>
			</table>
			<button class='btn btn-primary mt-3'
				onclick="this.style.display='none'; document.getElementById('hidealert').style.display='none'; window.print();">طباعة</button>
		</div>
		@else
		<div>
			<form action="{{route('form.jobs.store')}}" method="POST" enctype="multipart/form-data">
				@csrf
				<nav style="margin-bottom: 20px" class="navbar navbar-expand-lg navbar-light bg-light">
					<span class="navbar-text">
						المعلومات الشخصية
					</span>
				</nav>
				<!-- Start 0 -->
				<div class="form-group mb-3 row" style="position: relative;">
					<div class="file-upload" style="position: absolute; left: 0;top: 16px;">
						<div class="image-upload-wrap" style="@error('f3') border: 4px dashed #f00; @enderror">
							<input name="f1" class="file-upload-input" type='file' onchange="readURL(this);"
								accept=".png, .jpg, .jpeg" required />
							<div class="drag-text">
								<h3>اسحب او اضغط لرفع صورة شخصية</h3>
							</div>
						</div>
						<div class="file-upload-content">
							<img class="file-upload-image" src="#" alt="your image" />
							<div class="image-title-wrap">
								<button type="button" onclick="removeUpload()" class="remove-image">حذف الصورة</button>
							</div>
						</div>
						@error('f1')
						<span class="invalid-feedback d-block text-center" role="alert">
							<strong>{{ $message }}</strong>
						</span>
						@enderror
					</div>
				</div>
				<!-- End 0 -->
				<!-- Start 1 -->
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">الاسم الرباعي واللقب</label>
					<div class="col-sm-6">
						<input name="f2" type="text" class="form-control @error('f2') is-invalid @enderror"
							value="{{ old('f2') }}" required>
						@error('f2')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<!-- End 1 -->
				<!-- Start 2 -->
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">اسم الام الثلاثي</label>
					<div class="col-sm-6">
						<input type="text" class="form-control @error('f3') is-invalid @enderror" name="f3"
							value="{{ old('f3') }}" required>
						@error('f3')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<!-- End 2 -->
				<!-- Start 3 -->
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">تاريخ الميلاد</label>
					<div class="col-sm-3">
						<input type="date" class="form-control @error('f4') is-invalid @enderror" autocomplete="off"
							name="f4" value="{{ old('f4') }}" required>
						@error('f4')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<div class="col-sm-3">
						<select class="form-control @error('f5') is-invalid @enderror" name="f5" required>
							<option value="">محل الولادة</option>
							<option value="كركوك">كركوك</option>
							<option value="بغداد">بغداد</option>
							<option value="البصرة">البصرة</option>
							<option value="ميسان">ميسان</option>
							<option value="ذي قار">ذي قار</option>
							<option value="الديوانية">الديوانية</option>
							<option value="المثنى">المثنى</option>
							<option value="النجف الاشرف">النجف الاشرف</option>
							<option value="كربلاء المقدسة">كربلاء المقدسة</option>
							<option value="بابل">بابل</option>
							<option value="واسط">واسط</option>
							<option value="ديالى">ديالى</option>
							<option value="صلاح الدين">صلاح الدين</option>
							<option value="نينوى">نينوى</option>
							<option value="الانبار">الانبار</option>
							<option value="اربيل">اربيل</option>
							<option value="دهوك">دهوك</option>
							<option value="سليمانية">سليمانية</option>
						</select>
						@error('f5')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">الجنس</label>
					<div class="col-sm-6">
						<select class="form-control @error('f6') is-invalid @enderror" name="f6" required>
							<option value="">...</option>
							<option value="ذكر">ذكر</option>
							<option value="انثى">انثى</option>
						</select>
						@error('f6')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">رقم الهاتف</label>
					<div class="col-sm-6">
						<input type="number" placeholder="الرجاء ادخال رقم هاتف صحيح للتواصل من خلال رقم الهاتف "
							class="form-control @error('f7') is-invalid @enderror" name="f7" value="{{ old('f7') }}"
							required>
						@error('f7')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">البريد الالكتروني</label>
					<div class="col-sm-6">
						<input type="email" class="form-control" placeholder="ان وجد" name="f8" value="{{ old('f8') }}">
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">هل انت من الاقليات</label>
					<div class="col-sm-2">
						<select class="form-control @error('f9') is-invalid @enderror" name="f9" required>
							<option value="">اختر</option>
							<option value="نعم">نعم</option>
							<option value="كلا">كلا</option>
						</select>
						@error('f9')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<div class="col-sm-3">
						<select class="form-control" style="display:none" name="f10">
							<option value="">اختر</option>
							<option value="مسيحي">مسيحي</option>
							<option value="صابئي">صابئي</option>
							<option value="ايزيدي">ايزيدي</option>
							<option value="شبك">شبك</option>
							<option value="كردي فيلي">كردي فيلي</option>
						</select>
					</div>
					<div class="col-sm-4">
						<input type="file" accept=".png, .jpg, .jpeg"
							class="form-control @error('f11') is-invalid @enderror" style="display:none" name="f11">
						@error('f11')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<div class="col-sm-1 text-end">
						<button type="button" class="btn btn-secondary question-circle" style="display:none"
							data-toggle="tooltip" data-placement="top"
							title="يجب رفع مستمسك ثبوتي يؤيده ديوان اوقاف والجمعيات التابعين لها الأقليات لتضمن عدم استبعادك من نقاط المفاضلة">?</button>
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">الحالة الزوجية</label>
					<div class="col-sm-2">
						<select class="form-control @error('f12') is-invalid @enderror" name="f12" required>
							<option value="">اختر</option>
							<option value="اعزب">اعزب</option>
							<option value="متزوج">متزوج</option>
							<option value="مطلق">مطلق</option>
							<option value="ارمل">ارمل</option>
						</select>
						@error('f12')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<div class="col-sm-2">
						<select class="form-control" style="display: none;" name="f13">
							<option value="">عدد الاولاد</option>
							@for ($i = 0; $i <= 20; $i++) <option value="{{$i}}">{{$i}}</option>
								@endfor
						</select>
					</div>
					<div class="col-sm-2">
						<input placeholder="اسم الزوج" style="display: none;" type="text" class="form-control"
							name="f14" value="{{ old('f14') }}">
					</div>
					<div class="col-sm-2">
						<select class="form-control" style="display: none;" name="f15">
							<option value="">المهنة</option>
							<option value="موظف حكومي">موظف حكومي</option>
							<option value="كاسب">كاسب</option>
							<option value="عاطل">عاطل</option>
							<option value="ربة منزل">ربة منزل</option>
						</select>
					</div>
					<div class="col-sm-2">
						<select class="form-control" style="display: none;" name="f16">
							<option value="">يتقاضى راتب من الدولة</option>
							<option value="نعم">نعم</option>
							<option value="كلا">كلا</option>
						</select>
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-4 col-form-label">هل انت من ذوي الاحتياجات الخاصة</label>
					<div class="col-sm-2">
						<select class="form-control @error('f17') is-invalid @enderror" name="f17" required>
							<option value="">اختر</option>
							<option value="نعم">نعم</option>
							<option value="كلا">كلا</option>
						</select>
						@error('f17')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<div class="col-sm-3">
						<input type="text" placeholder="جهة اصدار الكتاب او التأييد" style="display: none;"
							class="form-control" name="f18" value="{{ old('f2') }}">
					</div>
					<div class="col-sm-2">
						<input type="file" accept=".png, .jpg, .jpeg" class="form-control" style="display: none;"
							name="f19">
					</div>
					<div class="col-sm-1 text-end">
						<button type="button" class="btn btn-secondary question-circle2" style="display: none;"
							data-toggle="tooltip" data-placement="top"
							title="رفع كتاب او تأييد او تقرير لجان طبية يثبت من ذوي الاحتياجات الخاصة">?</button>
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-10 col-form-label">هل مقدم الطلب من ذوي الشهداء والسجناء السياسين وشهداء الحشد
						الشعبي وضحايا الإرهاب والعمليات الحربية والاخطاء العسكرية وبحسب النسبة المقرر من القوانين
						النافذة الخاصة بالشرائح المذكورة </label>
					<div class="col-sm-2">
						<select class="form-control @error('f20') is-invalid @enderror" name="f20" required>
							<option value="">اختر</option>
							<option value="نعم">نعم</option>
							<option value="كلا">كلا</option>
						</select>
						@error('f20')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<div class="form-group mb-3 row martyrsfamilies" style="display: none;">
					<div class="col-sm-3">
						<input type="text" placeholder="جهة اصدار الكتاب او التأييد" class="form-control" name="f21">
					</div>
					<div class="col-sm-2">
						<select name="f22" class="form-control">
							<option value="">صلة القرابة</option>
							<option value="اب">اب</option>
							<option value="ام">ام</option>
							<option value="اخ">اخ</option>
							<option value="اخت">اخت</option>
							<option value="زوج">زوج</option>
							<option value="زوجة">زوجة</option>
							<option value="جد">جد</option>
							<option value="جدة">جدة</option>
							<option value="عم">عم</option>
							<option value="عمة">عمة</option>
						</select>
					</div>
					<div class="col-sm-6">
						<input type="file" accept=".png, .jpg, .jpeg" class="form-control" name="f23">
					</div>
					<div class="col-sm-1 text-end">
						<button type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="top"
							title="رفع كتاب او تأييد يثبت انك من ذوي الشهداء">?</button>
					</div>
				</div>
				<!-- End 3 -->
				<!-- Start 4 -->
				<nav style="margin-bottom: 20px" class="navbar navbar-expand-lg navbar-light bg-light">
					<span class="navbar-text">
						معلومات بطاقة السكن
					</span>
				</nav>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">رقم الاستمارة</label>
					<div class="col-sm-3">
						<input type="text" class="form-control @error('f24') is-invalid @enderror" name="f24"
							value="{{ old('f24') }}" required>
						@error('f24')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<label class="col-sm-2 col-form-label">تاريخ التنظيم</label>
					<div class="col-sm-2">
						<input type="date" class="form-control @error('f26') is-invalid @enderror" autocomplete="off"
							name="f26" value="{{ old('f26') }}" required>
						@error('f26')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<label class="col-sm-1 col-form-label">القضاء</label>
					<div class="col-sm-2 district">
						<select class="form-control @error('f27') is-invalid @enderror" id="changedistrict" required
							name="f27">
							<option value="">اختر</option>
							<option value="قضاء كركوك">قضاء كركوك</option>
							<option value="قضاء داقوق">قضاء داقوق</option>
							<option value="قضاء الدبس">قضاء الدبس</option>
							<option value="قضاء الحويجة">قضاء الحويجة</option>
						</select>
						@error('f27')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">الناحية</label>
					<div class="col-sm-3">
						<select class="form-control @error('f28') is-invalid @enderror" name="f28" required>
							<option value="">اختر</option>
						</select>
						@error('f28')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<label class="col-sm-2 col-form-label">الحي</label>
					<div class="col-sm-2">
						<input type="text" class="form-control @error('f29') is-invalid @enderror" name="f29"
							value="{{ old('f29') }}" required>
						@error('f29')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<label class="col-sm-1 col-form-label">دار</label>
					<div class="col-sm-2">
						<input type="text" class="form-control @error('f30') is-invalid @enderror" name="f30"
							value="{{ old('f30') }}" required>
						@error('f30')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<!-- End 4 -->
				<!-- Start 5 -->
				<nav style="margin-bottom: 20px" class="navbar navbar-expand-lg navbar-light bg-light">
					<span class="navbar-text">
						المعلومات الثبوتية
					</span>
				</nav>
				<div class="form-group mb-3 row">
					<div class="col-sm-3">
						<select class="form-control @error('f31') is-invalid @enderror" name="f31" required>
							<option value="">نوع المستمسكات</option>
							<option value="هويه الاحوال">هويه الاحوال</option>
							<option value="بطاقة وطنية">بطاقة وطنية</option>
						</select>
						@error('f31')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<div class="form-group mb-3 row idnational" style="display: none;">
					<label class="col-sm-2 col-form-label">رقم البطاقه الوطنية</label>
					<div class="col-sm-2">
						<input type="text" class="form-control" name="ff32" value="{{ old('ff32') }}">
					</div>
					<label class="col-sm-2 col-form-label">محل اصدارها</label>
					<div class="col-sm-2">
						<select class="form-control" name="ff33">
							<option value="كركوك">كركوك</option>
							<option value="بغداد">بغداد</option>
							<option value="البصرة">البصرة</option>
							<option value="ميسان">ميسان</option>
							<option value="ذي قار">ذي قار</option>
							<option value="الديوانية">الديوانية</option>
							<option value="المثنى">المثنى</option>
							<option value="النجف الاشرف">النجف الاشرف</option>
							<option value="كربلاء المقدسة">كربلاء المقدسة</option>
							<option value="بابل">بابل</option>
							<option value="واسط">واسط</option>
							<option value="ديالى">ديالى</option>
							<option value="صلاح الدين">صلاح الدين</option>
							<option value="نينوى">نينوى</option>
							<option value="الانبار">الانبار</option>
							<option value="اربيل">اربيل</option>
							<option value="دهوك">دهوك</option>
							<option value="سليمانية">سليمانية</option>
						</select>
					</div>
					<label class="col-sm-2 col-form-label">تاريخ اصدارها</label>
					<div class="col-sm-2">
						<input type="date" class="form-control" autocomplete="off" name="ff34"
							value="{{ old('ff34') }}">
					</div>
				</div>
				<div class="form-group mb-3 idnational2 row" style="display: none;">
					<label class="col-sm-2 col-form-label">رقم هوية الاحوال</label>
					<div class="col-sm-2">
						<input type="text" class="form-control" name="f32" value="{{ old('f32') }}">
					</div>
					<label class="col-sm-2 col-form-label">محل اصدارها</label>
					<div class="col-sm-2">
						<select class="form-control" name="f33">
							<option value="كركوك">كركوك</option>
							<option value="بغداد">بغداد</option>
							<option value="البصرة">البصرة</option>
							<option value="ميسان">ميسان</option>
							<option value="ذي قار">ذي قار</option>
							<option value="الديوانية">الديوانية</option>
							<option value="المثنى">المثنى</option>
							<option value="النجف الاشرف">النجف الاشرف</option>
							<option value="كربلاء المقدسة">كربلاء المقدسة</option>
							<option value="بابل">بابل</option>
							<option value="واسط">واسط</option>
							<option value="ديالى">ديالى</option>
							<option value="صلاح الدين">صلاح الدين</option>
							<option value="نينوى">نينوى</option>
							<option value="الانبار">الانبار</option>
							<option value="اربيل">اربيل</option>
							<option value="دهوك">دهوك</option>
							<option value="سليمانية">سليمانية</option>
						</select>
					</div>
					<label class="col-sm-2 col-form-label">تاريخ اصدارها</label>
					<div class="col-sm-2">
						<input type="date" class="form-control" autocomplete="off" name="f34" value="{{ old('f34') }}">
					</div>
				</div>
				<div class="form-group mb-3 idnational2 row" style="display: none;">
					<label class="col-sm-2 col-form-label">السجل</label>
					<div class="col-sm-2">
						<input type="text" class="form-control" autocomplete="off" name="f36" value="{{ old('f36') }}">
					</div>
					<label class="col-sm-2 col-form-label">الصحيفة</label>
					<div class="col-sm-2">
						<input type="text" class="form-control" autocomplete="off" name="f37" value="{{ old('f37') }}">
					</div>
					<label class="col-sm-2 col-form-label">رقم شهادة الجنسية</label>
					<div class="col-sm-2">
						<input type="text" class="form-control" autocomplete="off" name="f38" value="{{ old('f38') }}">
					</div>
				</div>
				<div class="form-group mb-3 idnational2 row" style="display: none;">
					<label class="col-sm-2 col-form-label">محل اصدارها</label>
					<div class="col-sm-2">
						<select class="form-control" name="f39">
							<option value="كركوك">كركوك</option>
							<option value="بغداد">بغداد</option>
							<option value="البصرة">البصرة</option>
							<option value="ميسان">ميسان</option>
							<option value="ذي قار">ذي قار</option>
							<option value="الديوانية">الديوانية</option>
							<option value="المثنى">المثنى</option>
							<option value="النجف الاشرف">النجف الاشرف</option>
							<option value="كربلاء المقدسة">كربلاء المقدسة</option>
							<option value="بابل">بابل</option>
							<option value="واسط">واسط</option>
							<option value="ديالى">ديالى</option>
							<option value="صلاح الدين">صلاح الدين</option>
							<option value="نينوى">نينوى</option>
							<option value="الانبار">الانبار</option>
							<option value="اربيل">اربيل</option>
							<option value="دهوك">دهوك</option>
							<option value="سليمانية">سليمانية</option>
						</select>
					</div>
					<label class="col-sm-2 col-form-label">تاريخ اصدارها</label>
					<div class="col-sm-2">
						<input type="date" class="form-control" autocomplete="off" name="f40" value="{{ old('f40') }}">
					</div>
				</div>
				<div class="form-group mb-3 idnational3 row" style="display: none;">
					<label class="col-sm-3 col-form-label mt-3">رفع المستمسكات الثبوتية</label>
					<div class="col-sm-9">
						<input type="file" accept=".pdf" class="form-control mt-3 @error('f35') is-invalid @enderror"
							autocomplete="off" name="f35">
						@error('f35')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<div class="mt-3 mr-3">
						<ul>
							<li>
								يجب توفير الأوراق الثبوتية الاتية.
								<ul>
									<li>الهوية الشخصية</li>
									<li>شهادة الجنسية في حال لديك هوية الاحوال الشخصية</li>
									<li>بطاقة السكن</li>
									<li>البطاقة التموينية</li>
								</ul>
							<li>على ان تكون صفحة واحة في ملف PDF.
								<a href="" data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight"
									aria-controls="offcanvasRight">مثال اضغط هنا</a>.
							</li>
							</li>
						</ul>
					</div>
				</div>
				<!-- End 5 -->
				<!-- Start 6 -->
				<nav style="margin-bottom: 20px" class="navbar navbar-expand-lg navbar-light bg-light">
					<span class="navbar-text">
						معلومات التحصيل الدراسي
					</span>
				</nav>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">التحصيل الدراسي</label>
					<div class="col-sm-2">
						<select class="form-control @error('f41') is-invalid @enderror" name="f41" required>
							<option value="">اختر</option>
							<option value="دبلوم">دبلوم</option>
							<option value="بكالوريوس">بكالوريوس</option>
						</select>
						@error('f41')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>

					<label class="col-sm-2 col-form-label">التقدير</label>
					<div class="col-sm-2">
						<select name="f43" class="form-control @error('f43') is-invalid @enderror" required>
							<option value="">اختر</option>
							<option value="مقبول">مقبول</option>
							<option value="متوسط">متوسط</option>
							<option value="جيد">جيد</option>
							<option value="جيد جدا">جيد جدا</option>
							<option value="امتياز">امتياز</option>
						</select>
						@error('f43')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<label class="col-sm-2 col-form-label">سنة التخرج</label>
					<div class="col-sm-2 mb-3">
						<select name="f44" class="form-control @error('f44') is-invalid @enderror" required>
							<option value="">اختر</option>
							@for ($i = date('Y') - 1; $i >= 1980; $i--)
							<option value="{{ $i . '-' . ($i + 1) }}">{{ $i . '-' . ($i + 1) }}</option>
							@endfor
						</select>
						@error('f44')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>

					<div class="col-sm-2">
						<select name="f45" class="form-control @error('f45') is-invalid @enderror" required>
							<option value="">نوع الدراسة</option>
							<option value="حكومي - صباحي">حكومي - صباحي</option>
							<option value="حكومي - مسائي">حكومي - مسائي</option>
							<option value="اهلي - صباحي">اهلي - صباحي</option>
							<option value="اهلي - مسائي">اهلي - مسائي</option>
						</select>
						@error('f45')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<div class="col-sm-2">
						<select name="f46" class="form-control @error('f46') is-invalid @enderror" required>
							<option value="">الدور</option>
							<option value="الاول">الاول</option>
							<option value="الثاني">الثاني</option>
						</select>
						@error('f46')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<label class="col-sm-2 col-form-label">وثيقة دراسية</label>
					<div class="col-sm-6">
						<input type="file" class="form-control @error('f47') is-invalid @enderror" autocomplete="off"
							accept=".png, .jpg, .jpeg" name="f47" required>
						@error('f47')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label">الدوائر التابعة للقضاء</label>
					<div class="col-sm-4">
						<select name="ff47" class="form-control @error('ff47') is-invalid @enderror" required>
							<option value="">اختر</option>
						</select>
						@error('ff47')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<label class="col-sm-2 col-form-label">الاختصاص</label>
					<div class="col-sm-4">
						<select name="ff48" class="form-control @error('ff48') is-invalid @enderror" required>
							<option value="">اختر</option>
						</select>
						@error('ff48')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<!-- End 6 -->
				<!-- Start 7 -->
				<nav style="margin-bottom: 20px" class="navbar navbar-expand-lg navbar-light bg-light">
					<span class="navbar-text">
						المهارات
					</span>
				</nav>
				<div class="form-group mb-3 row">
					<label class="col-sm-3 col-form-label">اللغات</label>
					<div class="col-sm-4">
						<input type="text" class="form-control" autocomplete="off" name="f48" value="{{ old('f48') }}">
					</div>
					<small style="font-size: 13px;" class="col-sm-5 col-form-label text-danger">ملاحظ:- افصل بين اللغات
						بــ( , ) مثال: كردي,تركماني,انكليزي</small>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-3 col-form-label">الحاسوب</label>
					<div class="col-sm-4">
						<input type="text" class="form-control" autocomplete="off" name="f49" value="{{ old('f49') }}">
					</div>
					<small style="font-size: 13px;" class="col-sm-5 col-form-label text-danger">ملاحظ:- افصل بين مهارات
						الحاسوب بــ( , ) مثال: وورد,اكسل,فوتوشوب</small>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-3 col-form-label">مهارات اخرى</label>
					<div class="col-sm-4">
						<input type="text" class="form-control" autocomplete="off" name="f50" value="{{ old('f50') }}">
					</div>
					<small style="font-size: 13px;" class="col-sm-5 col-form-label text-danger">ملاحظ:- افصل بين
						المهارات بــ( , )</small>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-3 col-form-label">شهادات معتمدة من منظمات</label>
					<div class="col-sm-4">
						<input type="text" class="form-control" autocomplete="off" name="f51" value="{{ old('f51') }}">
					</div>
					<small style="font-size: 13px;" class="col-sm-5 col-form-label text-danger">ملاحظ:- افصل بين
						الشهادات بــ( , )</small>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-3 col-form-label">رفع الشهادات</label>
					<div class="col-sm-4">
						<input type="file" accept=".png, .jpg, .jpeg" class="form-control" autocomplete="off"
							name="f52">
					</div>
				</div>
				<!-- End 7 -->
				<!-- Start 8 -->

				<nav style="margin-bottom: 20px" class="navbar navbar-expand-lg navbar-light bg-light">
					<span class="navbar-text">
						مكان العمل السابق
					</span>
				</nav>
				<div class="form-group mb-3 row">
					<label class="col-sm-12 col-form-label">هل انت موظف او متعاقد او عامل حالياً او سابقاً في دائرة او
						مؤسسة رسمية او شبه رسمية او مستفيد من الرعاية الاجتماعية او اي رواتب اخرى من الخزينة العامة
						للدولة؟</label>
					<div class="col-sm-2">
						<select name="f53" class="form-control @error('f53') is-invalid @enderror" required>
							<option value="">اختر</option>
							<option value="نعم">نعم</option>
							<option value="كلا">كلا</option>
						</select>
						@error('f53')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<label class="col-sm-2 col-form-label d1" style="display: none">اسم الدائرة</label>
					<div class="col-sm-3">
						<input type="text" name="f54" class="form-control" style="display: none"
							value="{{ old('f54') }}">
					</div>
					<label class="col-sm-2 col-form-label d2" style="display: none">الوظيفة</label>
					<div class="col-sm-3">
						<input type="text" name="f55" class="form-control" style="display: none"
							value="{{ old('f55') }}">
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label"></label>
					<label class="col-sm-2 col-form-label d3" style="display: none">تاريخ المباشرة</label>
					<div class="col-sm-3">
						<input type="date" name="f56" class="form-control" style="display: none"
							value="{{ old('f56') }}">
					</div>
					<label class="col-sm-2 col-form-label d4" style="display: none">تاريخ الانفكاك</label>
					<div class="col-sm-3">
						<input type="date" name="f57" class="form-control" style="display: none"
							value="{{ old('f57') }}">
					</div>
				</div>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label d5" style="display: none">اسباب ترك الوظيفة</label>
					<div class="col-sm-5">
						<textarea name="f58" class="form-control" cols="30" rows="10"
							style="display: none">{{ old('f58') }}</textarea>
					</div>
				</div>
				<!-- End 8 -->
				<nav style="margin-bottom: 20px" class="navbar navbar-expand-lg navbar-light bg-light">
					<span class="navbar-text">
						التعهد الخطي
					</span>
				</nav>
				<div class="form-group mb-3 row">
					<label class="col-sm-2 col-form-label mt-3">التعهد الخطي الاول</label>
					<div class="col-sm-4">
						<input type="file" accept=".pdf" class="form-control mt-3 @error('f59') is-invalid @enderror"
							autocomplete="off" name="f59" required>
						@error('f59')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
					<label class="col-sm-2 col-form-label mt-3">التعهد الخطي الثاني</label>
					<div class="col-sm-4">
						<input type="file" accept=".pdf" class="form-control mt-3 @error('f60') is-invalid @enderror"
							autocomplete="off" name="f60" required>
						@error('f60')
						<span class="text-danger">{{ $message }}</span>
						@enderror
					</div>
				</div>
				<hr>
				<!-- Start 9 -->
				<div style="width: 300px; margin: 0 auto!important">
					<div class="form-group">
						{!! NoCaptcha::renderJs() !!}
						{!! NoCaptcha::display() !!}
						<span class="text-danger">{{ $errors->first('g-recaptcha-response') }}</span>
					</div>
				</div>
				<div class="text-center mb-5 mt-3">
					<button type="submit" class="btn btn-primary">ارسال</button>
				</div>
				<!-- End 9 -->
			</form>
		</div>
		@endif
	</div>
	<!-- Bootstrap JS -->
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
		integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous">
	</script>
	<script src="{{ asset('public/jobs/js/main.js') }}"></script>
</body>

</html>
@else
<!doctype html>
<html dir="rtl">

<head>
	<!-- Required meta tags -->
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

	<!-- Bootstrap CSS -->
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
		integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">

	<title>ديوان محافظة كركوك</title>
</head>

<body class="text-center" style="background: #fdfdfd;">
	<img src="{{asset('public/jobs/images/7947201373594678_Untitled-1.webp')}}" width="76%" alt="">
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"
		integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct" crossorigin="anonymous">
	</script>
</body>

</html>
@endif