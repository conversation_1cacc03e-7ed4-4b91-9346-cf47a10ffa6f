<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Identity2 extends Model
{
    protected $table = 'identities2';
    protected $primaryKey = 'id';
    protected $fillable = [
        'f1',
        'f2',
        'f3',
        'f4',
        'f5',
        'ff4',
        'ff5',
        'ff6',
        'ff7',
        'fff7',
        'ffff7',
        'ff8',
        'ff9',
        'f6',
        'f7',
        'f1new',
        'f2new',
        'f3new',
        'f4new',
        'f5new',
        'f6new',
        'f8',
        'f9',
        'f10',
        'f11',
        'addf1',
        'addf2',
        'f12',
        'f13',
        'f14',
        'f15',
        'f16',
        'f17',
        'f18',
        'f19',
        'f20',
        'f21',
        'f22',
        'f23',
        'f24',
        'f25',
        'f26',
        'f27',
        'f28',
        'f29',
        'f30',
        'f31',
        'f32',
        'f33',
        'job_number',
        'updateid',
        'status',
        'date_status',
        'howmuchupdate',
        'IP',
        'BrowserAndReports',
        'update_identity',
    ];
    public function idnumber2()
    {
        return $this->hasOne('App\Idnumber2', 'identities_id');
    }
}
