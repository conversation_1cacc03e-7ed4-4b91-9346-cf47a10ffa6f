@extends('library.layout.main')
@section('title', 'المكتبة المركزية كركوك')
@section('content')
<!-- Categories -->

<ul class="nav justify-content-center" id="myTab" role="tablist">
  <li class="nav-item" role="presentation">
    <a class="nav-link disabled" aria-selected="false" aria-disabled="true"><i class="fas fa-chevron-right"></i></a>
  </li>
  <li class="nav-item" role="presentation">
    <a class="nav-link active" id="sec1-tab" data-toggle="tab" href="#sec1" role="tab" aria-controls="sec1" aria-selected="true">التصنيفات الرئيسية</a>
  </li>
  <li class="nav-item" role="presentation">
    <a class="nav-link" id="sec2-tab" data-toggle="tab" href="#sec2" role="tab" aria-controls="sec2" aria-selected="false">الكتب الجديدة</a>
  </li>
  <li class="nav-item" role="presentation">
    <a class="nav-link" id="sec3-tab" data-toggle="tab" href="#sec3" role="tab" aria-controls="sec3" aria-selected="false">البحث عن كتاب</a>
  </li>
  <li class="nav-item" role="presentation">
    <a class="nav-link" id="sec4-tab" data-toggle="tab" href="#sec4" role="tab" aria-controls="sec4" aria-selected="false">اخبار المكتبة</a>
  </li>
  <li class="nav-item" role="presentation">
    <a class="nav-link disabled" aria-selected="false" aria-disabled="true"><i class="fas fa-chevron-left"></i></a>
  </li>
</ul>
<div class="tab-content" id="myTabContent">
  <div class="tab-pane fade show active" id="sec1" role="tabpanel" aria-labelledby="sec1-tab">
<div class="main-content">
  @foreach ($categories as $cat)
    @if ($cat->parent == 0)
      <div class="wrapper">
        <a href="#" data-modal="#modalCustom{{$cat->id}}" class="modal__trigger text-decoration-none">
          <div class="book">
            <div class="book__cover" style="background-image:url('{{ url('public/library/images/categories/' . $cat->image) }}');">
            <div class="book__detail">{{ $cat->name }}</div>
            </div>
            <div class="book__page"></div>
          </div>
        </a>
        </div>
    
      <div id="modalCustom{{$cat->id}}" class="modal__1 modal__bg" role="dialog" aria-hidden="true">
        <div class="modal__dialog">
        <div class="modal__content">
        <h1 class="customH1">{{ $cat->name }}</h1>
        <div class="text-right">
          <p><u>الاقسام الفرعية</u></p>
          <ul>
              @foreach ($parents as $parent)
                  @if ($parent->parent == $cat->id)
                    <li><a onclick="customClick('{{ route('library.fetchBookByCat', $parent->id) }}')" href="#">{{ $parent->name }}</a></li>
                  @endif
              @endforeach 
          </ul>
        </div>
      <!-- modal close button -->
        <a href="" class="modal__close demo-close">
          <svg class="" viewBox="0 0 24 24"><path d="M19 6.41l-1.41-1.41-5.59 5.59-5.59-5.59-1.41 1.41 5.59 5.59-5.59 5.59 1.41 1.41 5.59-5.59 5.59 5.59 1.41-1.41-5.59-5.59z"/><path d="M0 0h24v24h-24z" fill="none"/></svg>
        </a>

      </div>
      </div>
    </div>
    @endif
  @endforeach
</div>
  </div>
  <div class="tab-pane fade" id="sec2" role="tabpanel" aria-labelledby="sec2-tab">
  </div>
  <div class="tab-pane fade" id="sec3" role="tabpanel" aria-labelledby="sec3-tab">البحث عن كتاب</div>
  <div class="tab-pane fade" id="sec4" role="tabpanel" aria-labelledby="sec4-tab">اخبار المكتبة</div>
</div>
@endsection